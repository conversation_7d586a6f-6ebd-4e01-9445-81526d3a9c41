const { htmlToText } = require('html-to-text');
const getConnection = require("../common/getConnection");
const doceboService = require("../../docebo/services");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;


function extractTextFromHtml(htmlContent) {
    return htmlContent.replace(/<[^>]+>/g, '');
}

// FIX: Add missing tidyCourseData function for batch processing
function tidyCourseData(courseInfo, courseListedInfo) {
    const { Name, CreatedById, LastModifiedById, ...rest } = courseTemplate;
    let tmpCourse = rest;

    // Prepare course data (same logic as createNewCourse)
    tmpCourse.Course_Code__c = courseInfo.code;
    tmpCourse.Course_Creation_Date__c = courseInfo.created_on == null ? "" : new Date(courseInfo.created_on).toISOString();
    tmpCourse.Course_Duration__c = courseInfo.time_options.duration.days;
    tmpCourse.Course_End_Date__c = courseInfo.time_options.date_end == null ? "" : new Date(courseInfo.time_options.date_end).toISOString();
    tmpCourse.Course_External_Id__c = courseInfo.id;
    tmpCourse.Course_Internal_ID__c = courseInfo.id;
    tmpCourse.Course_Name__c = courseInfo.name;
    tmpCourse.Course_Start_Date__c = courseInfo.time_options.date_begin == null ? "" : new Date(courseInfo.time_options.date_begin).toISOString();
    tmpCourse.Course_Status__c = courseInfo.status;
    tmpCourse.Course_Type__c = courseInfo.type;
    tmpCourse.Type__c = "A"; // Use valid picklist value
    tmpCourse.Description__c = extractTextFromHtml(courseInfo.description);
    tmpCourse.Language__c = courseInfo.language.name;
    tmpCourse.Last_Update_Date__c = courseInfo.updated_on == null ? "" : new Date(courseInfo.updated_on).toISOString();

    // FIX: Map category fields from actual Docebo data instead of hardcoding "A"
    tmpCourse.Course_Category__c = courseInfo.category ? courseInfo.category.name : "";
    tmpCourse.Course_Category_Code__c = courseInfo.category ? courseInfo.category.code : "";

    // If category data is not in courseInfo, try to get it from courseListedInfo
    if (!courseInfo.category && courseListedInfo && courseListedInfo.category) {
        tmpCourse.Course_Category__c = courseListedInfo.category.name || "";
        tmpCourse.Course_Category_Code__c = courseListedInfo.category.code || "";
    }

    // Since fields are now text fields, we can leave them empty if no category data
    // No fallback needed for text fields - empty strings are fine

    tmpCourse.Number_of_actions__c = courseListedInfo.actions ? courseListedInfo.actions.length : 0;
    tmpCourse.Number_of_sessions__c = courseListedInfo.sessions_count || 0;
    // Removed hardcoded OwnerId - let Salesforce use default owner
    tmpCourse.Skills_in_course__c = JSON.stringify(courseInfo.skills);
    tmpCourse.Slug__c = courseInfo.slug_name;
    tmpCourse.Thumbnail__c = courseInfo.thumbnail ? courseInfo.thumbnail.url : "";
    tmpCourse.Training_Material_Time_sec__c = courseInfo.average_completion_time;
    tmpCourse.Course_Link__c = `${APP_BASE}/learn/courses/${courseInfo.id}/${courseInfo.slug_name}`;
    tmpCourse.Effective__c = courseInfo.status == 'published' ? true : false;
    tmpCourse.Deleted__c = courseListedInfo.is_deleted;
    tmpCourse.Deletion_Date__c = courseListedInfo.removed_at ? new Date(courseListedInfo.removed_at).toISOString() : "";
    tmpCourse.Score__c = courseInfo.credits || 0;

    return tmpCourse;
}

const courseTemplate = {
    Training_Material_from_Mobile_App__c: 0.0,
    Course_Category__c: "",
    Course_Category_Code__c: "",
    Course_Code__c: "",
    Course_Creation_Date__c: "",
    Course_Duration__c: 0,
    Course_End_Date__c: "",
    Course_External_Id__c: 0,
    Course_has_expired__c: "",
    Course_Internal_ID__c: 0,
    Course_Link__c: "",
    Course_Name__c: "",
    Course_Progress__c: 0.0,
    Course_Start_Date__c: "",
    Course_Status__c: "",
    Course_Type__c: "",
    Name: "",
    CreatedById: "",
    Credits_CEUs__c: 0,
    Deleted__c: false,
    Deletion_Date__c: "",
    Description__c: "",
    Effective__c: false,
    Enrollment_Date__c: "",
    Language__c: "",
    LastModifiedById: "",
    Last_Update_Date__c: "",
    Number_of_actions__c: 0,
    Number_of_sessions__c: 0,
    // OwnerId removed - let Salesforce use default owner
    Reset_Password_Link__c: "",
    Score__c: 0,
    Session_Time_min__c: 0,
    Skills_in_course__c: "",
    Slug__c: "",
    Thumbnail__c: "",
    Time_in_Training_Material_from_Mobile_Ap__c: 0,
    Training_Material_Access_from_Mobile_App__c: 0,
    Training_Material_Time_sec__c: 0,
    Type__c: "",
    User_Course_Level__c: "",
    // docebo_v3__Deeplink__c: "",

}

async function getCourseSalesForceId(courseCode) {
    const conn = await getConnection();
    if (conn.accessToken) {
        const record = await conn.sobject("Docebo_Course__c")
            .findOne({ Course_External_Id__c: parseInt(courseCode) })
            .then(record => {
                if (!record) {
                    console.log("Course doesn't exist in Salesforce");
                    return null;
                }
                console.log(`Course found. ID: ${record.Id}`);
                return record.Id; // Return the record ID
            })
            .catch(err => {
                console.error("Error finding record:", err);
                throw err; // Rethrow error for further handling
            });
        return record;
    }
}

async function createNewCourse(courseData) {
    console.log("==== Here is auto course creation");
    const { Name, CreatedById, LastModifiedById, ...rest } = courseTemplate;
    let tmpCourse = rest;
    let courseListedInfo = await doceboService.getCourseListedInfo(courseData.id);
    
    try {
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("Invalid Salesforce connection in createNewCourse");
            return null;
        }
        
        // Prepare course data
        tmpCourse.Course_Code__c = courseData.code || courseData.uid;
        tmpCourse.Course_Creation_Date__c = courseData.created_on == null ? "" : new Date(courseData.created_on).toISOString();
        tmpCourse.Course_Duration__c = courseData.time_options.duration.days;
        // Use fallback logic for start/end dates
        tmpCourse.Course_Start_Date__c = courseData.time_options.date_begin
            ? new Date(courseData.time_options.date_begin).toISOString()
            : (courseData.created_on ? new Date(courseData.created_on).toISOString() : "");

        tmpCourse.Course_End_Date__c = courseData.time_options.date_end
            ? new Date(courseData.time_options.date_end).toISOString()
            : (courseData.created_on && courseData.time_options.duration.days
                ? new Date(new Date(courseData.created_on).getTime() + (courseData.time_options.duration.days * 24 * 60 * 60 * 1000)).toISOString()
                : "");

        tmpCourse.Course_External_Id__c = courseData.id;
        tmpCourse.Course_Internal_ID__c = courseData.id;
        tmpCourse.Course_Name__c = courseData.name;
        tmpCourse.Course_Status__c = courseData.status;
        tmpCourse.Course_Type__c = courseData.type;

        // FIX: Map Type__c to valid picklist value (only "A" is valid)
        tmpCourse.Type__c = "A"; // Use valid picklist value instead of courseData.type

        tmpCourse.Description__c = extractTextFromHtml(courseData.description);
        tmpCourse.Language__c = courseData.language.name;
        tmpCourse.Last_Update_Date__c = courseData.updated_on == null ? "" : new Date(courseData.updated_on).toISOString();

        // FIX: Map category fields from actual Docebo data instead of hardcoding "A"
        tmpCourse.Course_Category__c = courseData.category ? courseData.category.name : "";

        // Use category code if available, otherwise use category path as fallback
        if (courseData.category && courseData.category.code) {
            tmpCourse.Course_Category_Code__c = courseData.category.code;
        } else if (courseData.category && courseData.category.path && courseData.category.path.length > 0) {
            // Use the category path as category code (e.g., "Docebo/e-learning")
            tmpCourse.Course_Category_Code__c = courseData.category.path.join('/');
            console.log(`   📂 Using category path as code: ${tmpCourse.Course_Category_Code__c}`);
        } else {
            tmpCourse.Course_Category_Code__c = "";
        }

        // If category data is not in courseData, try to get it from courseListedInfo
        if (!courseData.category && courseListedInfo && courseListedInfo.category) {
            tmpCourse.Course_Category__c = courseListedInfo.category.name || "";
            if (courseListedInfo.category.code) {
                tmpCourse.Course_Category_Code__c = courseListedInfo.category.code;
            } else if (courseListedInfo.category.path && courseListedInfo.category.path.length > 0) {
                tmpCourse.Course_Category_Code__c = courseListedInfo.category.path.join('/');
                console.log(`   📂 Using courseListedInfo category path as code: ${tmpCourse.Course_Category_Code__c}`);
            } else {
                tmpCourse.Course_Category_Code__c = "";
            }
        }

        // Log category mapping for debugging
        console.log(`📂 Course Category Mapping:`);
        console.log(`   Course_Category__c: "${tmpCourse.Course_Category__c}"`);
        console.log(`   Course_Category_Code__c: "${tmpCourse.Course_Category_Code__c}"`);

        // Since fields are now text fields, we can leave them empty if no category data
        // No fallback needed for text fields - empty strings are fine

        // FIX: Use courseListedInfo.actions instead of thumbnail.actions
        tmpCourse.Number_of_actions__c = (courseListedInfo && courseListedInfo.actions) ? courseListedInfo.actions.length : 0;
        tmpCourse.Number_of_sessions__c = courseListedInfo.sessions_count || 0; // FIX: Use actual session count
        // Removed hardcoded OwnerId - let Salesforce use default owner
        tmpCourse.Skills_in_course__c = JSON.stringify(courseData.skills);
        tmpCourse.Slug__c = courseData.slug_name;
        tmpCourse.Thumbnail__c = courseData.thumbnail ? courseData.thumbnail.url : ""; // FIX: Use empty string instead of 0
        tmpCourse.Training_Material_Time_sec__c = courseData.average_completion_time;
        tmpCourse.Course_Link__c = `${APP_BASE}/learn/courses/${courseData.id}/${courseData.slug_name}`; // FIX: This should work
        tmpCourse.Effective__c = courseData.status == 'published' ? true : false;
        tmpCourse.Deleted__c = courseListedInfo.is_deleted;
        tmpCourse.Language__c = courseData.language.name; // FIX: Use courseData.language.name for consistency

        // FIX: Add deletion date if course is deleted
        tmpCourse.Deletion_Date__c = courseListedInfo.removed_at ? new Date(courseListedInfo.removed_at).toISOString() : "";

        // FIX: Add score from credits
        tmpCourse.Score__c = courseData.credits || 0;

        // FIX: Add missing fields that were requested
        // Course Category Code - already handled above
        // Course Start Date - already handled above
        // Course End Date - already handled above

        // Session Time (min) - Calculate from session data if available
        if (courseListedInfo && courseListedInfo.sessions && courseListedInfo.sessions.length > 0) {
            // Calculate total session time in minutes from all sessions
            let totalSessionTimeMinutes = 0;
            courseListedInfo.sessions.forEach(session => {
                if (session.date_start && session.date_end) {
                    const startTime = new Date(session.date_start.replace(' ', 'T'));
                    const endTime = new Date(session.date_end.replace(' ', 'T'));
                    const sessionDurationMs = endTime - startTime;
                    const sessionDurationMinutes = Math.round(sessionDurationMs / (1000 * 60));
                    totalSessionTimeMinutes += sessionDurationMinutes;
                }
            });
            tmpCourse.Session_Time_min__c = totalSessionTimeMinutes;
        } else {
            // Fallback: Use average_completion_time from Docebo (in seconds), convert to minutes
            if (courseData.average_completion_time) {
                tmpCourse.Session_Time_min__c = Math.round(courseData.average_completion_time / 60);
            } else {
                // Last fallback: Use course duration in days converted to minutes (assuming 8 hours per day)
                const courseDurationDays = courseData.time_options?.duration?.days || 0;
                tmpCourse.Session_Time_min__c = courseDurationDays * 8 * 60; // 8 hours per day * 60 minutes per hour
            }
        }

        // Enrollment Date - This should be set when processing enrollments, not at course level
        // But we can set a default enrollment start date if available from catalog options
        if (courseData.catalog_options?.self_enrollment?.start_date) {
            tmpCourse.Enrollment_Date__c = new Date(courseData.catalog_options.self_enrollment.start_date.replace(' ', 'T')).toISOString();
        } else {
            // Use course creation date as fallback for enrollment availability
            tmpCourse.Enrollment_Date__c = tmpCourse.Course_Creation_Date__c;
        }

        console.log(`📊 Course Field Mapping Summary:`);
        console.log(`   Course_Category_Code__c: "${tmpCourse.Course_Category_Code__c}"`);
        console.log(`   Course_Start_Date__c: "${tmpCourse.Course_Start_Date__c}"`);
        console.log(`   Course_End_Date__c: "${tmpCourse.Course_End_Date__c}"`);
        console.log(`   Session_Time_min__c: ${tmpCourse.Session_Time_min__c}`);
        console.log(`   Enrollment_Date__c: "${tmpCourse.Enrollment_Date__c}"`);

        // Use upsert instead of create to handle duplicates
        console.log(`Upserting course with External ID: ${tmpCourse.Course_External_Id__c}`);
        const courseResult = await conn.sobject("Docebo_Course__c").upsert(
            tmpCourse,
            'Course_External_Id__c'
        );
        
        if (courseResult.success) {
            console.log(`Course upserted successfully: ${courseData.id}`);
            
            // Try to get the ID of the upserted record
            const record = await conn.sobject("Docebo_Course__c")
                .findOne({ Course_External_Id__c: courseData.id });
                
            return record ? record.Id : null;
        } else {
            console.error(`Failed to upsert course ${courseData.id}:`, courseResult.errors);
            return null;
        }
    } catch (error) {
        console.error(`Error in createNewCourse for course ${courseData.id}:`, error);
        
        // Handle the specific duplicate value error gracefully
        if (error.errorCode === 'DUPLICATE_VALUE') {
            console.log(`Course ${courseData.id} already exists, fetching existing ID`);
            try {
                // Get the existing course ID
                const conn = await getConnection();
                const record = await conn.sobject("Docebo_Course__c")
                    .findOne({ Course_External_Id__c: courseData.id });
                
                return record ? record.Id : null;
            } catch (findError) {
                console.error(`Error finding existing course ${courseData.id}:`, findError);
                return null;
            }
        }
        
        return null;
    }
}

async function getTotalCourseList() {
    const conn = await getConnection();
    if (!conn.accessToken) {
        console.error("No valid Salesforce connection.");
        return [];
    }

    try {
        const salesforceCourses = await conn.sobject("Docebo_Course__c")
            .find({ Course_External_Id__c: { $ne: null } }, "Course_External_Id__c")
            .execute();

        return salesforceCourses.map(course => course.Course_External_Id__c);
    } catch (err) {
        console.error("Error fetching Salesforce courses:", err);
        return [];
    }
}

async function saveCoursesInBatch(courses) {
    const batchSize = 200; // Define the batch size
    const conn = await getConnection();

    if (!conn.accessToken) {
        console.error("No valid Salesforce connection.");
        return false;
    }

    for (let i = 0; i < courses.length; i += batchSize) {
        const batch = courses.slice(i, i + batchSize);
        console.log(`Processing batch ${i / batchSize + 1}: ${batch.length} courses`);

        const batchData = batch.map(course => {
            const tmpCourseInfo = tidyCourseData(course.courseInfo, course.courseListedInfo);
            return {
                attributes: { type: "Docebo_Course__c" },
                ...tmpCourseInfo
            };
        });

        try {
            const result = await conn.sobject("Docebo_Course__c").create(batchData, { allOrNone: false });

            result.forEach((res, index) => {
                if (res.success) {
                    console.log(`Course ${batch[index].courseInfo.id} saved successfully.`);
                } else {
                    console.error(`Failed to save course ${batch[index].courseInfo.id}:`, res.errors);
                }
            });
        } catch (err) {
            console.error("Error during batch save:", err);
        }
    }
}

module.exports = {
    createNewCourse,
    getTotalCourseList,
    saveCoursesInBatch,
    getCourseSalesForceId,
    tidyCourseData // FIX: Export the tidyCourseData function
}