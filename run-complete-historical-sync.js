#!/usr/bin/env node

/**
 * Complete Historical Data Synchronization Script
 * 
 * This script runs all the dedicated bulk sync files in the correct order
 * to synchronize all historical data from Docebo to Salesforce.
 */

const { spawn } = require('child_process');
const path = require('path');

// Define the sync steps in order
const syncSteps = [
    {
        name: "User Historical Sync",
        description: "Sync all users (creates Lead<PERSON>, Contacts, Docebo_Users__c)",
        file: "other/run-historical-data-update.js",
        critical: true
    },
    {
        name: "Complete Course Bulk Sync", 
        description: "Sync all courses from Docebo",
        file: "other/complete-all-courses-bulk-sync.js",
        critical: true
    },
    {
        name: "Update Courses with Categories",
        description: "Add category information to existing courses", 
        file: "other/update-courses-with-categories-fixed.js",
        critical: false
    },
    {
        name: "Course Enrollment Bulk Sync",
        description: "Sync course enrollments (fixes missing 5,544 enrollments)",
        file: "other/complete-course-43-bulk-sync.js", 
        critical: true
    },
    {
        name: "Learning Plan Enrollment Bulk Sync",
        description: "Sync learning plan enrollments",
        file: "other/complete-learning-plan-enrollment-bulk-sync.js",
        critical: true
    },
    {
        name: "Learning Plan Course Enrollment Bulk Sync", 
        description: "Sync learning plan course enrollments (junction records)",
        file: "other/complete-learning-plan-course-enrollment-bulk-sync.js",
        critical: true
    },
    {
        name: "Session Attendance Bulk Sync",
        description: "Sync session attendance records",
        file: "other/complete-session-attendance-bulk-sync.js",
        critical: false
    },
    {
        name: "Process Instructor Associations",
        description: "Associate instructors with sessions",
        file: "other/process-all-instructor-associations.js",
        critical: false
    }
];

async function runScript(scriptPath) {
    return new Promise((resolve, reject) => {
        console.log(`\n🚀 Starting: ${scriptPath}`);
        console.log(`⏰ Started at: ${new Date().toISOString()}`);
        
        const child = spawn('node', [scriptPath], {
            stdio: 'inherit',
            cwd: process.cwd()
        });
        
        child.on('close', (code) => {
            if (code === 0) {
                console.log(`✅ Completed: ${scriptPath}`);
                console.log(`⏰ Finished at: ${new Date().toISOString()}`);
                resolve();
            } else {
                console.error(`❌ Failed: ${scriptPath} (exit code: ${code})`);
                reject(new Error(`Script failed with exit code ${code}`));
            }
        });
        
        child.on('error', (error) => {
            console.error(`💥 Error running ${scriptPath}:`, error);
            reject(error);
        });
    });
}

async function runCompleteSync() {
    console.log('🎯 COMPLETE HISTORICAL DATA SYNCHRONIZATION');
    console.log('=' .repeat(60));
    console.log(`📅 Started at: ${new Date().toISOString()}`);
    console.log(`📊 Total steps: ${syncSteps.length}`);
    console.log('');
    
    let completedSteps = 0;
    let failedSteps = 0;
    const startTime = Date.now();
    
    for (let i = 0; i < syncSteps.length; i++) {
        const step = syncSteps[i];
        
        console.log(`\n📋 STEP ${i + 1}/${syncSteps.length}: ${step.name}`);
        console.log(`📝 Description: ${step.description}`);
        console.log(`📁 File: ${step.file}`);
        console.log(`🔥 Critical: ${step.critical ? 'Yes' : 'No'}`);
        
        try {
            await runScript(step.file);
            completedSteps++;
            console.log(`\n✅ STEP ${i + 1} COMPLETED SUCCESSFULLY`);
        } catch (error) {
            failedSteps++;
            console.error(`\n❌ STEP ${i + 1} FAILED:`, error.message);
            
            if (step.critical) {
                console.error(`\n🚨 CRITICAL STEP FAILED - STOPPING SYNC`);
                console.error(`💡 Fix the issue and restart from step ${i + 1}`);
                process.exit(1);
            } else {
                console.log(`\n⚠️ Non-critical step failed - continuing...`);
            }
        }
        
        // Add a small delay between steps
        if (i < syncSteps.length - 1) {
            console.log('\n⏳ Waiting 5 seconds before next step...');
            await new Promise(resolve => setTimeout(resolve, 5000));
        }
    }
    
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000 / 60); // minutes
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 COMPLETE HISTORICAL SYNC FINISHED');
    console.log('=' .repeat(60));
    console.log(`📅 Finished at: ${new Date().toISOString()}`);
    console.log(`⏱️ Total duration: ${duration} minutes`);
    console.log(`✅ Completed steps: ${completedSteps}/${syncSteps.length}`);
    console.log(`❌ Failed steps: ${failedSteps}/${syncSteps.length}`);
    
    if (failedSteps === 0) {
        console.log('\n🎯 ALL HISTORICAL DATA SYNCHRONIZED SUCCESSFULLY!');
        console.log('🔗 Your webhooks are now ready to handle real-time updates.');
    } else {
        console.log(`\n⚠️ ${failedSteps} non-critical steps failed. Review logs above.`);
    }
}

// Handle script interruption
process.on('SIGINT', () => {
    console.log('\n\n🛑 Sync interrupted by user');
    console.log('💡 You can restart the sync at any time by running this script again');
    process.exit(0);
});

// Run the complete sync
runCompleteSync().catch((error) => {
    console.error('\n💥 Fatal error in sync process:', error);
    process.exit(1);
});
