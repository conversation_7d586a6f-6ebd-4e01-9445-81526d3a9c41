#!/bin/bash

# Start script for Docebo-Salesforce Integration
# This script starts the application with proper permissions for SSL

echo "🚀 Starting Docebo-Salesforce Integration..."

# Check if we're in the right directory
if [ ! -f "app.js" ]; then
    echo "❌ app.js not found. Please run this script from the application directory."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please create your environment configuration."
    exit 1
fi

# Check if SSL certificates exist (for production)
if [ "$NODE_ENV" = "production" ]; then
    if [ ! -f "/etc/ssl/private/server.key" ] || [ ! -f "/etc/ssl/certs/server.crt" ]; then
        echo "⚠️  SSL certificates not found. Run ./setup-ssl.sh first for HTTPS support."
        echo "   Continuing with HTTP only..."
    fi
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Check if authbind is available and configured for port 443
if command -v authbind >/dev/null 2>&1 && [ -f "/etc/authbind/byport/443" ]; then
    echo "🔒 Starting with authbind (allows binding to port 443)..."
    authbind --deep node app.js
else
    echo "📝 Starting without authbind (HTTP only on port 3000)..."
    echo "   For HTTPS support, run ./setup-ssl.sh first"
    node app.js
fi
