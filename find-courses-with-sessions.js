#!/usr/bin/env node

/**
 * Find Courses with Sessions
 *
 * This script finds courses that have sessions for testing purposes
 */

require('dotenv').config();
const doceboService = require("./platform/docebo/services");

async function findCoursesWithSessions(maxCoursesToCheck = 20) {
    try {
        console.log(`🔍 FINDING COURSES WITH SESSIONS (checking first ${maxCoursesToCheck} courses)`);
        console.log('=' .repeat(60));
        
        // Get course list
        console.log('📋 Fetching course list from Docebo...');
        const courseListedInfo = await doceboService.getTotalCourseListedInfo();
        
        if (!courseListedInfo || courseListedInfo.length === 0) {
            console.log('⚠️ No courses found in Docebo');
            return;
        }

        console.log(`✅ Found ${courseListedInfo.length} total courses in Docebo`);
        console.log(`🔍 Checking first ${maxCoursesToCheck} courses for sessions...\n`);

        const coursesWithSessions = [];
        const coursesToCheck = courseListedInfo.slice(0, maxCoursesToCheck);

        for (let i = 0; i < coursesToCheck.length; i++) {
            const course = coursesToCheck[i];
            const courseId = course.id;
            
            console.log(`📚 ${i + 1}/${coursesToCheck.length}: Checking course ${courseId} (${course.name || course.title})...`);
            
            try {
                const sessionListedInfo = await doceboService.getSessionListedInfo(courseId);
                
                if (sessionListedInfo && sessionListedInfo.length > 0) {
                    console.log(`   ✅ Found ${sessionListedInfo.length} sessions!`);
                    coursesWithSessions.push({
                        id: courseId,
                        name: course.name || course.title,
                        type: course.type,
                        sessionCount: sessionListedInfo.length,
                        sessions: sessionListedInfo.map(s => ({
                            id: s.id,
                            name: s.name,
                            date_start: s.date_start,
                            date_end: s.date_end
                        }))
                    });
                } else {
                    console.log(`   ⚪ No sessions (${course.type} course)`);
                }
                
            } catch (error) {
                console.log(`   ❌ Error checking sessions: ${error.message}`);
            }
        }

        console.log('\n' + '='.repeat(60));
        console.log('🎉 SEARCH COMPLETED!');
        console.log(`📊 Found ${coursesWithSessions.length} courses with sessions:`);
        console.log('');

        if (coursesWithSessions.length > 0) {
            coursesWithSessions.forEach((course, index) => {
                console.log(`${index + 1}. Course ${course.id}: ${course.name}`);
                console.log(`   Type: ${course.type}`);
                console.log(`   Sessions: ${course.sessionCount}`);
                course.sessions.forEach((session, sessionIndex) => {
                    console.log(`     ${sessionIndex + 1}. Session ${session.id}: ${session.name || 'Unnamed'}`);
                    console.log(`        Start: ${session.date_start || 'Not set'}`);
                    console.log(`        End: ${session.date_end || 'Not set'}`);
                });
                console.log('');
            });

            console.log('🎯 RECOMMENDED TEST COMMANDS:');
            coursesWithSessions.slice(0, 3).forEach((course, index) => {
                console.log(`${index + 1}. node test-session-sync.js ${course.id}  # ${course.name} (${course.sessionCount} sessions)`);
            });
        } else {
            console.log('⚠️ No courses with sessions found in the first 20 courses.');
            console.log('💡 Try increasing the search range:');
            console.log('   node find-courses-with-sessions.js 50');
        }

    } catch (error) {
        console.error('❌ Error finding courses with sessions:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

// Parse command line arguments
const maxCoursesToCheck = parseInt(process.argv[2]) || 20;

// Run the search
findCoursesWithSessions(maxCoursesToCheck).catch(console.error);
