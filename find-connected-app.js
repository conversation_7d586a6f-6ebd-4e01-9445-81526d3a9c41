#!/usr/bin/env node

/**
 * Find Connected App Script
 * 
 * This script helps you find your current Connected App in Salesforce
 * using the Client ID from your working sandbox configuration.
 */

// Your current sandbox Client ID
const CURRENT_CLIENT_ID = "3MVG9ZqlCComSMtThZAVXpvedL0qQeQ3myKt.IAKWrX6WlgjQBPcWCyLEOCi27mzBxayz3Jv_xUMa1UiGgUPO";

console.log('🔍 FINDING YOUR CONNECTED APP');
console.log('=' .repeat(50));
console.log('');
console.log('📋 Your current sandbox credentials:');
console.log(`   Client ID: ${CURRENT_CLIENT_ID}`);
console.log(`   Sandbox URL: https://strivetogether--full.sandbox.my.salesforce.com`);
console.log(`   Username: <EMAIL>`);
console.log('');

console.log('🎯 TO FIND YOUR CONNECTED APP:');
console.log('');

console.log('📱 **Method 1: In Your Sandbox**');
console.log('   1. Log into your SANDBOX: https://test.salesforce.com');
console.log('   2. Go to Setup → App Manager (or Setup → Create → Apps)');
console.log('   3. Look for Connected Apps');
console.log('   4. Find the app with Consumer Key starting with: 3MVG9ZqlCComSMt...');
console.log('   5. Note the Connected App Name');
console.log('');

console.log('📱 **Method 2: Search by Client ID**');
console.log('   1. In Salesforce Setup, use the Quick Find box');
console.log('   2. Search for "Connected Apps"');
console.log('   3. Click "Connected Apps" under "Apps"');
console.log('   4. Look through the list for Consumer Key: 3MVG9ZqlCComSMt...');
console.log('');

console.log('📱 **Method 3: If You Can\'t Find It**');
console.log('   The app might be named something like:');
console.log('   - "Docebo Integration"');
console.log('   - "API Integration"'); 
console.log('   - "External Integration"');
console.log('   - "sf-hs-dev" (matching your Docebo client name)');
console.log('   - Or any custom name your team used');
console.log('');

console.log('🔧 **Once You Find the App Name:**');
console.log('   1. Create the SAME app in your PRODUCTION org');
console.log('   2. Use the same settings/configuration');
console.log('   3. Get the new Consumer Key and Consumer Secret');
console.log('   4. Update your .env file with production values');
console.log('');

console.log('💡 **Alternative: Create New Connected App**');
console.log('   If you can\'t find the existing app, create a new one:');
console.log('   1. Setup → App Manager → New Connected App');
console.log('   2. Basic Information:');
console.log('      - Connected App Name: Docebo Salesforce Integration');
console.log('      - API Name: Docebo_Salesforce_Integration');
console.log('      - Contact Email: <EMAIL>');
console.log('   3. API (Enable OAuth Settings):');
console.log('      - ✅ Enable OAuth Settings');
console.log('      - Callback URL: https://login.salesforce.com/services/oauth2/success');
console.log('      - Selected OAuth Scopes: Access and manage your data (api)');
console.log('   4. Save and get Consumer Key/Secret');
console.log('');

console.log('🎯 **Your Production Domain**');
console.log('   Based on your sandbox URL, your production domain is likely:');
console.log('   - https://strivetogether.my.salesforce.com');
console.log('   - Or https://strivetogether.lightning.force.com');
console.log('');

console.log('📝 **Next Steps:**');
console.log('   1. Find/create the Connected App in PRODUCTION');
console.log('   2. Update .env with production credentials');
console.log('   3. Run: node test-production-sf-connection.js');
console.log('   4. If successful, run: node run-complete-historical-sync.js');
console.log('');

console.log('❓ **Need Help?**');
console.log('   If you can\'t find the Connected App, let me know and I\'ll help');
console.log('   you create a new one with the exact same configuration.');
