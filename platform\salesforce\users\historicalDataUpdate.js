const getConnection = require("../common/getConnection");
const { tidyData } = require("./createUser");


// Optional file logging for test runs: set HISTORICAL_FILE_LOG=1 to enable
const fs = require('fs');
const path = require('path');
(function setupFileLogger() {
    try {
        if (process.env.HISTORICAL_FILE_LOG === '1') {
            const logsDir = path.join(process.cwd(), 'logs');
            if (!fs.existsSync(logsDir)) fs.mkdirSync(logsDir, { recursive: true });
            const ts = new Date().toISOString().replace(/[:.]/g, '-');
            const logPath = path.join(logsDir, `historical-sync-${ts}.log`);
            const stream = fs.createWriteStream(logPath, { flags: 'a' });

            const origLog = console.log.bind(console);
            const origWarn = console.warn.bind(console);
            const origError = console.error.bind(console);

            const safe = (v) => {
                if (v instanceof Error) return `${v.name}: ${v.message}`;
                if (typeof v === 'string') return v;
                try { return JSON.stringify(v); } catch { return String(v); }
            };
            const stamp = (lvl, args) => `[${new Date().toISOString()}] [${lvl}] ${args.map(safe).join(' ')}\n`;

            console.log = (...args) => { try { stream.write(stamp('INFO', args)); } catch {} origLog(...args); };
            console.warn = (...args) => { try { stream.write(stamp('WARN', args)); } catch {} origWarn(...args); };
            console.error = (...args) => { try { stream.write(stamp('ERROR', args)); } catch {} origError(...args); };

            origLog(`📝 File logging enabled: ${logPath}`);
        }
    } catch (e) {
        // If file logging setup fails, continue without interrupting the run
        console.warn('⚠️ Failed to initialize file logger:', e && (e.message || e.toString()));
    }
})();

// Helper functions to map Docebo values to valid Salesforce picklist values
function mapGenderToValidValue(doceboGender) {
    const genderMap = {
        'Male': 'Man',
        'Female': 'Woman',
        'Man': 'Man',
        'Woman': 'Woman',
        'Non-Binary': 'Non-Binary or other gender identity',
        'Non-binary': 'Non-Binary or other gender identity',
        'Other': 'Prefer not to respond',
        'Prefer not to say': 'Prefer not to respond',
        'Prefer Not To Say': 'Prefer not to respond'
    };
    return genderMap[doceboGender] || 'Prefer not to respond';
}

function mapRaceToValidValue(doceboRace) {
    const raceMap = {
        'White': 'White',
        'Black': 'Black or African American',
        'Hispanic': 'Hispanic or Latine',
        'Asian': 'Asian',
        'Native American': 'American Indian or Alaskan Native',
        'Pacific Islander': 'Native Hawaiian or Other Pacific Islander',
        'Multi-Racial': 'Multi-Racial',
        'Other': 'Other'
    };
    return raceMap[doceboRace] || 'Other';
}

function mapRoleTypeToValidValue(doceboRole) {
    const roleMap = {
        'Administrative': 'Administrative',
        'Board': 'Board of Directors',
        'Communications': 'Communications',
        'Community': 'Community Engagement/Organizing',
        'Data': 'Data and Research',
        'Executive': 'Executive Director',
        'Facilitator': 'Facilitator',
        'Fundraising': 'Fundraising/Development',
        'Leadership': 'Leadership Table Member',
        'Operations': 'Operations/Business Management',
        'Partnership': 'Partnership Table Member',
        'Policy': 'Policy/Government',
        'Programs': 'Programs',
        'Youth': 'Youth/Families/Community'
    };
    return roleMap[doceboRole] || 'Other';
}

// Helper function to extract a value/label from Docebo additional_fields array
function getAdditionalData(additionalFields, fieldId) {
    if (!additionalFields || !Array.isArray(additionalFields)) {
        return null;
    }

    let optionLabel = null;
    for (const element of additionalFields) {
        if (element.id == fieldId) {
            if (element.enabled === true) {
                if ('options' in element) {
                    for (const elOpt of element.options) {
                        if (elOpt.id == element.value) {
                            optionLabel = elOpt.label;
                            break;
                        }
                    }
                } else {
                    optionLabel = element.value;
                }
            }
        }
        if (optionLabel) break;
    }
    return optionLabel;
}

// Consistent with createUser.js usage: return the label for the selected State option
function getStateLabel(additionalFields, fieldId) {
    return getAdditionalData(additionalFields || [], fieldId) || "";
}


// Helper function to map user type based on learning plan enrollments and additional fields
function mapUserTypeToValidValue(userInfo, userListedInfo, additionalFields) {
    // First check if there's a specific additional field for Type__c (field ID 31, 32, etc.)
    // Add this when we discover the correct field ID
    const typeFromAdditionalField = getAdditionalData(additionalFields || [], "31"); // Placeholder - update with correct field ID
    if (typeFromAdditionalField) {
        const typeMapping = {
            "Network Member": "Network Member",
            "Backbone Staff": "Backbone Staff",
            "Backbone Partner": "Backbone Partner"
        };
        const mappedType = typeMapping[typeFromAdditionalField];
        if (mappedType) {
            console.log(`🎯 User type determined from additional field: ${mappedType}`);
            return mappedType;
        }
    }

    // TODO: Add logic to check learning plan enrollments
    // This would require querying the user's learning plan enrollments
    // and checking if they're enrolled in "Network Member" learning plans

    // For now, default to "Backbone Staff" but log that we need to implement learning plan logic
    console.log(`⚠️ User type defaulting to 'Backbone Staff' - learning plan enrollment logic needed`);
    return "Backbone Staff";
}

// Helper function to map contact type based on additional fields
function mapContactTypeToValidValue(additionalFields) {
    // Check if there's a specific additional field for Contact_Type__c
    const contactTypeFromAdditionalField = getAdditionalData(additionalFields || [], "32"); // Placeholder - update with correct field ID
    if (contactTypeFromAdditionalField) {
        const contactTypeMapping = {
            "Other": "Other",
            "Backbone Staff": "Backbone Staff",
            "Backbone Partner": "Backbone Partner"
        };
        const mappedContactType = contactTypeMapping[contactTypeFromAdditionalField];
        if (mappedContactType) {
            console.log(`🎯 Contact type determined from additional field: ${mappedContactType}`);
            return mappedContactType;
        }
    }

    // Default to "Other" if no specific mapping found
    return "Other";
}


async function updateHistoricalData(userLimit = null) {
    try {
        console.log('🔄 Starting Historical Data Update Process...');
        if (userLimit) {
            console.log(`🧪 TEST MODE: Processing only ${userLimit} users`);
        }

        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return false;
        }

        // Step 1: Fetch all users from Docebo API
        console.log('\n📥 Fetching users from Docebo API...');
        let doceboUsers = await fetchAllDoceboUsers(userLimit);

        if (!doceboUsers || doceboUsers.length === 0) {
            console.log('⚠️ No users found in Docebo API');
            return false;
        }

        // Limit users if specified (for testing)
        if (userLimit && userLimit > 0) {
            doceboUsers = doceboUsers.slice(0, userLimit);
            console.log(`🧪 Limited to ${doceboUsers.length} users for testing`);
        }

        console.log(`✅ Found ${doceboUsers.length} users in Docebo`);

        // Step 2: Process users in batches
        const batchSize = 50;
        let processedCount = 0;
        let updatedContacts = 0;
        let updatedLeads = 0;
        let createdLeads = 0;
        let updatedDoceboUsers = 0;
        let createdDoceboUsers = 0;
        let linkedToContacts = 0;
        let linkedToLeads = 0;

        for (let i = 0; i < doceboUsers.length; i += batchSize) {
            const batch = doceboUsers.slice(i, i + batchSize);
            console.log(`\n📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(doceboUsers.length / batchSize)} (${batch.length} users)`);

            for (const doceboUser of batch) {
                try {
                    const result = await processUser(conn, doceboUser);

                    if (result.contactUpdated) updatedContacts++;
                    if (result.leadUpdated) updatedLeads++;
                    if (result.leadCreated) createdLeads++;
                    if (result.doceboUserUpdated) updatedDoceboUsers++;
                    if (result.doceboUserCreated) createdDoceboUsers++;
                    if (result.contactLinked) linkedToContacts++;
                    if (result.leadLinked) linkedToLeads++;

                    processedCount++;

                    if (processedCount % 10 === 0) {
                        console.log(`   Processed ${processedCount}/${doceboUsers.length} users...`);
                    }

                } catch (userError) {
                    console.error(`❌ Error processing user ${doceboUser.userInfo?.user_data?.user_id}:`, userError.message);
                }
            }
        }

        // Step 3: Summary
        console.log('\n📊 HISTORICAL DATA UPDATE SUMMARY:');
        console.log('='.repeat(60));
        console.log(`📥 Total Docebo Users Processed: ${processedCount}`);
        console.log(`👤 Contacts Updated: ${updatedContacts}`);
        console.log(`🔗 DU Linked to Contacts: ${linkedToContacts}`);
        console.log(`🎯 Leads Updated: ${updatedLeads}`);
        console.log(`🆕 Leads Created: ${createdLeads}`);
        console.log(`🔗 DU Linked to Leads: ${linkedToLeads}`);
        console.log(`📋 Docebo_Users__c Updated: ${updatedDoceboUsers}`);
        console.log(`🆕 Docebo_Users__c Created: ${createdDoceboUsers}`);
        console.log('\n✅ Historical data update completed successfully!');

        return true;

    } catch (error) {
        console.error('💥 Error in historical data update:', error);
        return false;
    }
}

async function processUser(conn, doceboUser) {
    const result = {
        contactUpdated: false,
        contactLinked: false,
        leadUpdated: false,
        leadCreated: false,
        leadLinked: false,
        doceboUserUpdated: false,
        doceboUserCreated: false
    };

    try {
        // Transform Docebo data using existing function
        const tmpUserInfo = tidyData(doceboUser.userInfo, doceboUser.userListedInfo);
        const email = tmpUserInfo.Email__c;

        if (!email) {
            console.log(`⚠️ Skipping user ${tmpUserInfo.User_Unique_Id__c} - no email`);
            return result;
        }

        // Pre-fetch existing Contact or Lead by email (Contact has priority)
        const existingContacts = await conn.sobject("Contact").find({ Email: email }).limit(1).execute();
        const contactId = existingContacts.length > 0 ? existingContacts[0].Id : null;
        let leadId = null;
        if (!contactId) {
            const existingLeads = await conn.sobject("Lead").find({ Email: email, IsConverted: false }).limit(1).execute();
            leadId = existingLeads.length > 0 ? existingLeads[0].Id : null;
        }

        // Step 1: Create or Update Docebo_Users__c first
        const userUniqueId = Number(tmpUserInfo.User_Unique_Id__c);
        const existingDoceboUsers = await conn.sobject("Docebo_Users__c").find({ User_Unique_Id__c: userUniqueId }).limit(1).execute();
        let duId = null;
        if (existingDoceboUsers.length > 0) {
            duId = existingDoceboUsers[0].Id;
            tmpUserInfo.Id = duId;
            await conn.sobject("Docebo_Users__c").update(tmpUserInfo);
            result.doceboUserUpdated = true;
        } else {
            const duRes = await conn.sobject("Docebo_Users__c").create(tmpUserInfo);
            if (duRes && duRes.success) {
                duId = duRes.id;
                result.doceboUserCreated = true;
            } else {
                console.error("❌ Failed to create Docebo_Users__c:", duRes && duRes.errors);
                return result;
            }
        }

        // Step 2: Update and link Contact or Lead; else create Lead then link
        if (contactId) {
            // Update Contact with full mapping
            const contactUpdated = await updateContact(conn, contactId, tmpUserInfo, doceboUser.userInfo);
            if (contactUpdated) { result.contactUpdated = true; }

            // Align DU email to Contact email to satisfy lookup filters
            const contactRec = await conn.sobject("Contact").findOne({ Id: contactId });
            if (contactRec && contactRec.Email) {
                if (tmpUserInfo.Email__c !== contactRec.Email) {
                    try { await conn.sobject("Docebo_Users__c").update({ Id: duId, Email__c: contactRec.Email }); } catch {}
                }
                // Link DU to Contact and set back-reference
                const linkRes = await conn.sobject("Docebo_Users__c").update({ Id: duId, Contact__c: contactId, Email__c: contactRec.Email });
                if (!linkRes.success) {
                    console.warn("⚠️ Historical: Failed to link DU to Contact:", linkRes.errors);
                } else {
                    result.contactLinked = true;
                    console.log(`✅ Historical: Linked DU ${duId} to Contact ${contactId}`);
                    await setContactBackReference(conn, contactId, duId);
                }
            }
        } else if (leadId) {
            // Update Lead with full mapping
            const leadUpdatedOk = await updateLead(conn, leadId, tmpUserInfo, doceboUser.userInfo);
            if (leadUpdatedOk) { result.leadUpdated = true; }

            // Align DU email to Lead email and link
            const leadRec = await conn.sobject("Lead").findOne({ Id: leadId });
            if (leadRec && leadRec.Email) {
                if (tmpUserInfo.Email__c !== leadRec.Email) {
                    try { await conn.sobject("Docebo_Users__c").update({ Id: duId, Email__c: leadRec.Email }); } catch {}
                }
                const linkRes = await conn.sobject("Docebo_Users__c").update({ Id: duId, Lead__c: leadId, Email__c: leadRec.Email });
                if (!linkRes.success) {
                    console.warn("⚠️ Historical: Failed to link DU to Lead:", linkRes.errors);
                } else {
                    result.leadLinked = true;
                    console.log(`✅ Historical: Linked DU ${duId} to Lead ${leadId}`);
                    await setLeadBackReference(conn, leadId, duId);
                }
            }
        } else {
            // Create new Lead with full mapping
            const newLeadId = await createNewLead(conn, tmpUserInfo, doceboUser.userInfo);
            if (newLeadId) {
                // Align and link
                const leadRec = await conn.sobject("Lead").findOne({ Id: newLeadId });
                if (leadRec && leadRec.Email && tmpUserInfo.Email__c !== leadRec.Email) {
                    try { await conn.sobject("Docebo_Users__c").update({ Id: duId, Email__c: leadRec.Email }); } catch {}
                }
                const linkRes = await conn.sobject("Docebo_Users__c").update({ Id: duId, Lead__c: newLeadId, Email__c: (leadRec && leadRec.Email) || tmpUserInfo.Email__c });
                if (!linkRes.success) {
                    console.warn("⚠️ Historical: Failed to link DU to new Lead:", linkRes.errors);
                } else {
                    await setLeadBackReference(conn, newLeadId, duId);
                }
                result.leadCreated = true;
            }
        }

    } catch (error) {
        console.error(`Error processing user ${doceboUser.userInfo?.user_data?.user_id}:`, error.message);
    }

    return result;
}

async function updateContact(conn, contactId, tmpUserInfo, userInfo) {
    // Full Contact mapping aligned with createUser.js
    const contactUpdateData = {
        Id: contactId,
        // Standard fields
        FirstName: tmpUserInfo.First_Name__c || null,
        LastName: (tmpUserInfo.Last_Name__c && tmpUserInfo.Last_Name__c.trim() !== "") ? tmpUserInfo.Last_Name__c : "Unknown",
        Email: tmpUserInfo.Email__c || null,
        Title: tmpUserInfo.Job_Title__c || "",

        // FIX: Add comprehensive Contact field mappings
        Created_by_Docebo_API__c: true,
        GenderIdentity: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c),
        Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
        Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
        Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),

        // Additional fields with correct names
        Description: `Docebo user - Level: ${tmpUserInfo.User_Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
        Fax: getAdditionalData(userInfo.additional_fields || [], "26") || "",
        Phone: userInfo.user_data.phone || "",
        Languages__c: userInfo.user_data.language || "",
        mailingcity__c: getAdditionalData(userInfo.additional_fields || [], "24") || "",
        mailingcountry__c: getAdditionalData(userInfo.additional_fields || [], "28") || "",
        mailingpostalcode__c: getAdditionalData(userInfo.additional_fields || [], "29") || "",
        mailingstate__c: getStateLabel(userInfo.additional_fields || [], "25") || "",
        mailingstreet__c: getAdditionalData(userInfo.additional_fields || [], "30") || "",
        Position_Role__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c) || "",

        // FIX: Add missing required fields
        Annual_Revenue__c: tmpUserInfo.Annual_Revenue__c || 0,
        Company__c: tmpUserInfo.Organization_Name__c || "",
        Industry__c: tmpUserInfo.Industry__c || "Not For Profit",
        Initiative__c: tmpUserInfo.Initiative__c || "",
        LeadSource: "Docebo Platform",
        NumberOfEmployees__c: tmpUserInfo.NumberOfEmployees__c || 0,
        Organization_Employer__c: tmpUserInfo.Organization_Name__c || "",
        Rating__c: tmpUserInfo.Rating__c || "Warm",
        Time_Zone__c: userInfo.user_data.timezone || "",
        Type__c: "Backbone Staff",
        Website__c: tmpUserInfo.Organization_URL__c || "",
        Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",

        Active_Portal_User__c: tmpUserInfo.Email_Validation_Status__c || false,
        FTE__c: "Full-Time",
        Gateway__c: "Docebo API",
        Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
        Legacy_ID__c: String(tmpUserInfo.User_Unique_Id__c || ""),
        No_Longer_Leadership__c: false,
        No_Longer_Staff__c: false,
        Number_of_years_in_the_partnership__c: "0",
        Contact_Type__c: "Other"
    };

    const updateResult = await conn.sobject("Contact").update(contactUpdateData);
    return updateResult && updateResult.success;
}

async function updateLead(conn, leadId, tmpUserInfo, userInfo) {
    const leadUpdateData = {
        Id: leadId,
        LastName: tmpUserInfo.Last_Name__c && tmpUserInfo.Last_Name__c.trim() !== "" ? tmpUserInfo.Last_Name__c : "Unknown",
        FirstName: tmpUserInfo.First_Name__c,
        Email: tmpUserInfo.Email__c,
        Company: tmpUserInfo.Organization_Name__c || "-",
        Title: tmpUserInfo.Job_Title__c || "",
        Website: tmpUserInfo.Organization_URL__c || "",
        Organization_URL__c: tmpUserInfo.Organization_URL__c || "",

        // Custom fields mapped according to your field mapping list
        Created_by_Docebo_API__c: true,
        Gender__c: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c),
        Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
        Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
        Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),

        // Additional fields from your mapping - now with proper data
        Description: `Docebo user - Level: ${tmpUserInfo.User_Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
        Fax: getAdditionalData(userInfo.additional_fields || [], "26") || "",
        Salutation: getAdditionalData(userInfo.additional_fields || [], "27") || "",
        Phone: userInfo.user_data.phone || "",
        Languages__c: userInfo.user_data.language || "",
        mailingcity__c: getAdditionalData(userInfo.additional_fields || [], "24") || "",
        mailingcountry__c: getAdditionalData(userInfo.additional_fields || [], "28") || "",
        mailingpostalcode__c: getAdditionalData(userInfo.additional_fields || [], "29") || "",
        mailingstate__c: getStateLabel(userInfo.additional_fields || [], "25") || "",
        mailingstreet__c: getAdditionalData(userInfo.additional_fields || [], "30") || "",
        position_role__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c) || "",
        LeadSource: "Docebo Platform",

        // FIX: Add missing required fields
        accountid__c: tmpUserInfo.Account_ID__c || "",
        AnnualRevenue: tmpUserInfo.Annual_Revenue__c || 0, // Lead uses standard AnnualRevenue field
        Industry: tmpUserInfo.Industry__c || "Not For Profit",
        Initiative__c: tmpUserInfo.Initiative__c || "",
        NumberOfEmployees: tmpUserInfo.NumberOfEmployees__c || 0,
        Organization_employer__c: tmpUserInfo.Organization_Name__c || "",
        Rating: tmpUserInfo.Rating__c || "Warm",
        Time_Zone__c: tmpUserInfo.Time_Zone__c || "",
        Type__c: "Backbone Staff",
        Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",

        Active_Portal_User__c: tmpUserInfo.Email_Validation_Status__c || false,
        FTE__c: "Full-Time", // Default value
        Gateway__c: "Docebo API",
        Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
        Legacy_Id__c: String(tmpUserInfo.User_Unique_Id__c || ""),
        No_Longer_Leadership__c: false, // Default value
        No_Longer_Staff__c: false, // Default value
        Number_of_Years_in_the_Partnership__c: 0, // Not available in Docebo data
        //OwnerId: tmpUserInfo.OwnerId || "005O400000BxnnxIAB", - Not needed
        Contact_Type__c: "Other"
    };

    const res = await conn.sobject("Lead").update(leadUpdateData);
    return res && res.success;
}

async function createNewLead(conn, tmpUserInfo, userInfo) {
    // Create Lead only (no Account creation)
    const leadData = {
            // Standard Lead fields mapped according to your specifications
            LastName: tmpUserInfo.Last_Name__c && tmpUserInfo.Last_Name__c.trim() !== "" ? tmpUserInfo.Last_Name__c : "Unknown",
            FirstName: tmpUserInfo.First_Name__c,
            Email: tmpUserInfo.Email__c,
            Company: tmpUserInfo.Organization_Name__c || "-",
            Title: tmpUserInfo.Job_Title__c || "",
            Website: tmpUserInfo.Organization_URL__c || "",
            Organization_URL__c: tmpUserInfo.Organization_URL__c || "",
            Status: "Open - Not Contacted",

            // Custom fields mapped according to your field mapping list
            Created_by_Docebo_API__c: true,
            Gender__c: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c),
            Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
            Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
            Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),

            // Additional fields from your mapping - now with proper data
            Description: `Docebo user - Level: ${tmpUserInfo.User_Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
            Fax: getAdditionalData(userInfo.additional_fields || [], "26") || "",
            Salutation: getAdditionalData(userInfo.additional_fields || [], "27") || "",
            Phone: userInfo.user_data.phone || "",
            Languages__c: userInfo.user_data.language || "",
            mailingcity__c: getAdditionalData(userInfo.additional_fields || [], "24") || "",
            mailingcountry__c: getAdditionalData(userInfo.additional_fields || [], "28") || "",
            mailingpostalcode__c: getAdditionalData(userInfo.additional_fields || [], "29") || "",
            mailingstate__c: getStateLabel(userInfo.additional_fields || [], "25") || "",
            mailingstreet__c: getAdditionalData(userInfo.additional_fields || [], "30") || "",
            position_role__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c) || "",
            LeadSource: "Docebo Platform",

            // FIX: Add missing required fields
            accountid__c: tmpUserInfo.Account_ID__c || "",
            AnnualRevenue: tmpUserInfo.Annual_Revenue__c || 0, // Lead uses standard AnnualRevenue field
            Industry: tmpUserInfo.Industry__c || "Not For Profit", // Default to common value
            Initiative__c: tmpUserInfo.Initiative__c || "",
            NumberOfEmployees: tmpUserInfo.NumberOfEmployees__c || 0,
            Organization_employer__c: tmpUserInfo.Organization_Name__c || "",
            Rating: tmpUserInfo.Rating__c || "Warm", // Default rating
            Time_Zone__c: tmpUserInfo.Time_Zone__c || "",
            Type__c: "Backbone Staff", // Default type
            Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",

            Active_Portal_User__c: tmpUserInfo.Email_Validation_Status__c || false,
            FTE__c: "Full-Time", // Default value
            Gateway__c: "Docebo API",
            Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
            Legacy_Id__c: String(tmpUserInfo.User_Unique_Id__c || ""),
            No_Longer_Leadership__c: false, // Default value
            No_Longer_Staff__c: false, // Default value
            Number_of_Years_in_the_Partnership__c: 0, // Not available in Docebo data
            //OwnerId: tmpUserInfo.OwnerId || "005O400000BxnnxIAB", - Not needed
            Contact_Type__c: "Other"
        };

        const res = await conn.sobject("Lead").create(leadData);
        return res && res.success ? res.id : null;
}

// Import Docebo services
const doceboService = require("../../docebo/services");

async function fetchAllDoceboUsers(limit = null) {
    try {
        console.log('📥 Fetching all users from Docebo API...');

        // Get users from Docebo with optional limit to avoid fetching all pages in tests
        const allUsers = await doceboService.getTotalUserListedInfo(limit || undefined);

        if (!allUsers || allUsers.length === 0) {
            console.log('⚠️ No users found in Docebo');
            return [];
        }

        console.log(`✅ Found ${allUsers.length} users in Docebo`);

        // For each user, get detailed info including additional fields
        const usersWithDetails = [];
        let processedCount = 0;

        for (const user of allUsers) {
            try {
                // Get detailed user info with additional fields
                const userInfo = await doceboService.getUserInfo(user.user_id);

                if (userInfo.status === 200) {
                    const userData = userInfo.data;
                    userData["fired_at"] = user.creation_date || null;
                    userData["expiration_date"] = user.expiration_date || null;

                    usersWithDetails.push({
                        userInfo: userData,
                        userListedInfo: user
                    });
                }

                processedCount++;

                if (processedCount % 50 === 0) {
                    console.log(`   Processed ${processedCount}/${allUsers.length} users...`);
                }

            } catch (userError) {
                console.error(`Error fetching details for user ${user.user_id}:`, userError.message);
            }
        }

        console.log(`✅ Successfully processed ${usersWithDetails.length} users with detailed info`);
        return usersWithDetails;

    } catch (error) {
        console.error('💥 Error fetching users from Docebo:', error);
        return [];
    }
}


// Helper: set back-reference on Lead to point to Docebo_Users__c
async function setLeadBackReference(conn, leadId, doceboUserId) {
    try {
        if (!leadId || !doceboUserId) {
            console.log("ℹ️ Missing ids for back-reference, skipping", { leadId, doceboUserId });
            return false;
        }
        const leadDescribe = await conn.sobject("Lead").describe();
        const duLookup = leadDescribe.fields.find(f => f.name === 'Docebo_User__c');
        if (!duLookup) {
            console.log("ℹ️ Lead.Docebo_User__c field not found; skipping back-reference");
            return false;
        }
        const res = await conn.sobject("Lead").update({ Id: leadId, Docebo_User__c: doceboUserId });
        if (res.success) {
            console.log(`✅ Lead ${leadId} back-referenced to Docebo_Users__c ${doceboUserId}`);
            return true;
        } else {
            console.error("❌ Failed to set Lead.Docebo_User__c:", res.errors);
            return false;
        }
    } catch (e) {
        const msg = e && (e.message || e.errorCode || e.toString());
        console.error("❌ Error setting Lead back-reference:", msg);
        return false;
    }
}

// Helper: set back-reference on Contact to point to Docebo_Users__c
async function setContactBackReference(conn, contactId, doceboUserId) {
    try {
        if (!contactId || !doceboUserId) {
            console.log("ℹ️ Missing ids for Contact back-reference, skipping", { contactId, doceboUserId });
            return false;
        }
        const contactDescribe = await conn.sobject("Contact").describe();
        const duLookup = contactDescribe.fields.find(f => f.name === 'Docebo_User__c');
        if (!duLookup) {
            console.log("ℹ️ Contact.Docebo_User__c field not found; skipping back-reference");
            return false;
        }
        const res = await conn.sobject("Contact").update({ Id: contactId, Docebo_User__c: doceboUserId });
        if (res.success) {
            console.log(`✅ Contact ${contactId} back-referenced to Docebo_Users__c ${doceboUserId}`);
            return true;
        } else {
            console.error("❌ Failed to set Contact.Docebo_User__c:", res.errors);
            return false;
        }
    } catch (e) {
        const msg = e && (e.message || e.errorCode || e.toString());
        console.error("❌ Error setting Contact back-reference:", msg);
        return false;
    }
}

module.exports = {
    updateHistoricalData,
    processUser,
    fetchAllDoceboUsers
};
