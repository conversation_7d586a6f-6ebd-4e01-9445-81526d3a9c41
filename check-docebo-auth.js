#!/usr/bin/env node

require('dotenv').config();
const getAccessToken = require('./common/docebo/access-token');
const axios = require('axios');

async function main() {
  try {
    const tokenRes = await getAccessToken();
    if (tokenRes.status !== 200) {
      console.error('❌ Token fetch failed:', tokenRes.message || tokenRes);
      process.exit(1);
    }

    const token = tokenRes.accessToken;
    const masked = token ? `${token.slice(0, 8)}...${token.slice(-6)}` : 'n/a';
    console.log('✅ Token fetched successfully');
    console.log('   Token length:', token.length);
    console.log('   Token (masked):', masked);

    const base = process.env.DOCEBO_API_BASE_URL;
    if (!base) {
      console.warn('⚠️ DOCEBO_API_BASE_URL is not set; skipping API GET check.');
      process.exit(0);
    }

    // Minimal, read-only API call to confirm connectivity
    const testUrl = `${base}/manage/v1/user?page=1&page_size=1`;
    console.log('🌐 Testing API GET:', testUrl);
    const resp = await axios.get(testUrl, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    const items = resp?.data?.data?.items || [];
    console.log('✅ API GET succeeded. Items length:', items.length);
    if (items.length > 0) {
      const sample = items[0];
      console.log('   Sample user:', {
        user_id: sample.user_id,
        email: sample.email,
        firstname: sample.firstname,
        lastname: sample.lastname
      });
    }
  } catch (err) {
    const status = err.response?.status;
    const data = err.response?.data;
    console.error('💥 Error during auth/API check');
    if (status) console.error('   HTTP status:', status);
    if (data) console.error('   Response data:', data);
    console.error('   Message:', err.message);
    process.exit(1);
  }
}

main();

