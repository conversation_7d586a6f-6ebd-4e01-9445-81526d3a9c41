#!/usr/bin/env node
require('dotenv').config();

(async () => {
  try {
    const userId = process.argv[2];
    if (!userId) {
      console.error('Usage: node scripts/create-lead-from-docebo.js <docebo_user_id>');
      process.exit(2);
    }

    const doceboService = require('../platform/docebo/services');
    const { createNewUser, tidyData } = require('../platform/salesforce/users/createUser');
    const getConnection = require('../platform/salesforce/common/getConnection');

    console.log(`🔎 Fetching Docebo user ${userId} ...`);
    const userInfoRes = await doceboService.getUserInfo(userId);
    if (userInfoRes.status !== 200) {
      console.error('❌ Docebo getUserInfo failed:', userInfoRes && (userInfoRes.message || userInfoRes.status));
      process.exit(1);
    }
    const listedInfo = await doceboService.getUserListedInfo(userId);

    const userInfo = userInfoRes.data;
    console.log('📦 Docebo user fetched. Email:', userInfo?.user_data?.email);

    // Run the same creation logic used by the webhook
    console.log('🚀 Creating (or updating) user in Salesforce via createNewUser() ...');
    const ok = await createNewUser(userInfo, listedInfo);
    if (!ok) {
      console.error('❌ createNewUser returned false');
      process.exit(1);
    }

    const email = userInfo?.user_data?.email;
    const conn = await getConnection();
    if (!conn || !conn.accessToken) {
      console.error('❌ Could not obtain Salesforce connection after creation.');
      process.exit(2);
    }

    // Try to find the Lead by email first, then fallback to Contact
    console.log('🔎 Querying Salesforce for the new record by email ...');
    const leadQuery = `SELECT Id, Email, FirstName, LastName, Company, CreatedDate FROM Lead WHERE Email = '${email}' ORDER BY CreatedDate DESC LIMIT 1`;
    const leadRes = await conn.query(leadQuery);
    if (leadRes.records && leadRes.records.length > 0) {
      console.log(JSON.stringify({ type: 'Lead', record: leadRes.records[0] }, null, 2));
      process.exit(0);
    }

    const contactQuery = `SELECT Id, Email, FirstName, LastName, AccountId, CreatedDate FROM Contact WHERE Email = '${email}' ORDER BY CreatedDate DESC LIMIT 1`;
    const contactRes = await conn.query(contactQuery);
    if (contactRes.records && contactRes.records.length > 0) {
      console.log(JSON.stringify({ type: 'Contact', record: contactRes.records[0] }, null, 2));
      process.exit(0);
    }

    console.log('[]');
    process.exit(0);
  } catch (e) {
    console.error('💥 Failure:', e && (e.message || e.toString()));
    process.exit(1);
  }
})();

