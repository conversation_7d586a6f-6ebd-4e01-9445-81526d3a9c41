#!/usr/bin/env node
require('dotenv').config();

(async () => {
  try {
    const minutes = Number(process.argv[2] || process.env.FILTER_MINUTES || 20);
    const getConnection = require('../platform/salesforce/common/getConnection');
    const conn = await getConnection();
    if (!conn || !conn.accessToken) {
      console.error('❌ Could not obtain Salesforce connection.');
      process.exit(2);
    }
    const soql = `SELECT Id, Email, FirstName, LastName, Company, CreatedDate, Status
                  FROM Lead
                  WHERE CreatedDate = LAST_N_MINUTES:${minutes}
                  AND Created_by_Docebo_API__c = true
                  ORDER BY CreatedDate DESC
                  LIMIT 10`;
    const res = await conn.query(soql);
    if (!res || !res.records || res.records.length === 0) {
      console.log('[]');
      process.exit(0);
    }
    console.log(JSON.stringify(res.records, null, 2));
  } catch (e) {
    console.error('💥 Query failed:', e && (e.message || e.toString()));
    process.exit(1);
  }
})();

