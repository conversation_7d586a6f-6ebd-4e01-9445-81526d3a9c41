#!/usr/bin/env node



require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const doceboService = require("./platform/docebo/services");
const { tidyData } = require("./platform/salesforce/users/createUser");

async function simple5UserTest() {
    try {
        console.log('Simple 5 user test');
        
        // Step 1: Get Salesforce connection
        console.log('🔐 Getting Salesforce connection...');
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return false;
        }
        console.log('✅ Salesforce connection established');
        
        // Step 2: Get first 5 users from Docebo
        console.log('📥 Fetching first 5 users from Docebo...');
        const allUsers = await doceboService.getTotalUserListedInfo();
        
        if (!allUsers || allUsers.length === 0) {
            console.log('⚠️ No users found in Docebo');
            return false;
        }
        
        // Take only first 5 users
        const first5Users = allUsers.slice(0, 5);
        console.log(`✅ Got ${first5Users.length} users for testing`);
        
        // Step 3: Process each user
        let successCount = 0;
        let errorCount = 0;
        
        for (let i = 0; i < first5Users.length; i++) {
            const user = first5Users[i];
            console.log(`\nProcessing ${i + 1}/5 user_id ${user.user_id}: ${user.firstname || ''} ${user.lastname || ''}`);
            
            try {
                // Get detailed user info
                const detailedUser = await doceboService.getUserInfo(user.user_id);
                if (!detailedUser || detailedUser.status !== 200) {
                    console.log(`   ⚠️ Failed to get detailed info for user ${user.user_id}`);
                    continue;
                }
                console.log(`   Got Docebo details for ${user.user_id}`);

                // Process the user data (unwrap .data)
                const processedData = tidyData(detailedUser.data, user);
                console.log(`   Processed: ${processedData.Full_Name__c} <${processedData.Email__c || ''}>`);

                // Check if user exists in Salesforce
                const email = processedData.Email__c;
                if (!email) {
                    console.log(`   No email for user ${user.user_id}, skipping`);
                    continue;
                }

                // Search for existing Contact
                let contact = null;
                try {
                    const contactRes = await conn.sobject('Contact').find({ Email: email }).limit(1).execute();
                    contact = (contactRes && contactRes.length > 0) ? contactRes[0] : null;
                } catch (e) {
                    console.log(`   Contact lookup failed: ${e.message || e}`);
                }

                // Search for existing Lead (may be restricted by permissions)
                let lead = null;
                try {
                    const leadRes = await conn.sobject('Lead').find({ Email: email, IsConverted: false }).limit(1).execute();
                    lead = (leadRes && leadRes.length > 0) ? leadRes[0] : null;
                } catch (e) {
                    console.log(`   Lead lookup failed: ${e.message || e}`);
                }

                if (contact) {
                    console.log(`   Found Contact: ${contact.Id}`);
                    // Minimal, safe Contact update (no Account creation)
                    try {
                        const upd = await conn.sobject('Contact').update({
                            Id: contact.Id,
                            LastName: (processedData.Last_Name__c && processedData.Last_Name__c.trim() !== "") ? processedData.Last_Name__c : 'Unknown',
                            FirstName: processedData.First_Name__c || null,
                            Email: processedData.Email__c || null,
                            Title: processedData.Job_Title__c || '',
                            Active_Portal_User__c: !!processedData.Email_Validation_Status__c,
                            Created_by_Docebo_API__c: true,
                            LeadSource: 'Docebo Platform'
                        });
                        if (upd.success) {
                            console.log(`   ✏️ Contact updated: ${contact.Id}`);
                        } else {
                            console.log(`   ⚠️ Contact update failed:`, upd.errors);
                        }
                    } catch (e) {
                        console.log(`   Contact update error: ${e.message || e}`);
                    }

                    // Ensure Docebo_Users__c exists/updated
                    try {
                        const existingDU = await conn.sobject('Docebo_Users__c').findOne({ User_Unique_Id__c: processedData.User_Unique_Id__c });
                        if (existingDU) {
                            const duUpd = await conn.sobject('Docebo_Users__c').update({ ...processedData, Id: existingDU.Id });
                            console.log(duUpd.success ? `   ✅ Docebo_Users__c updated: ${existingDU.Id}` : `   ⚠️ Docebo_Users__c update failed: ${JSON.stringify(duUpd.errors)}`);
                        } else {
                            const duCreate = await conn.sobject('Docebo_Users__c').create(processedData);
                            console.log(duCreate.success ? `   ✅ Docebo_Users__c created: ${duCreate.id}` : `   ⚠️ Docebo_Users__c creation failed: ${JSON.stringify(duCreate.errors)}`);
                        }
                    } catch (e) {
                        console.log(`   ❌ Docebo_Users__c ensure error: ${e.message || e}`);
                    }
                } else if (lead) {
                    console.log(`   Found Lead: ${lead.Id}`);
                    // Minimal, safe Lead update
                    try {
                        const upd = await conn.sobject('Lead').update({
                            Id: lead.Id,
                            LastName: (processedData.Last_Name__c && processedData.Last_Name__c.trim() !== "") ? processedData.Last_Name__c : 'Unknown',
                            FirstName: processedData.First_Name__c || null,
                            Email: processedData.Email__c || null,
                            Company: processedData.Organization_Name__c || '-',
                            Title: processedData.Job_Title__c || '',
                            Website: processedData.Organization_URL__c || '',
                            Status: 'Open - Not Contacted',
                            Created_by_Docebo_API__c: true,
                            LeadSource: 'Docebo Platform'
                        });
                        if (upd.success) {
                            console.log(`   ✏️ Lead updated: ${lead.Id}`);
                        } else {
                            console.log(`   Lead update failed:`, upd.errors);
                        }
                    } catch (e) {
                        console.log(`   Lead update error: ${e.message || e}`);
                    }

                    // Ensure Docebo_Users__c exists/updated
                    try {
                        const existingDU = await conn.sobject('Docebo_Users__c').findOne({ User_Unique_Id__c: processedData.User_Unique_Id__c });
                        if (existingDU) {
                            const duUpd = await conn.sobject('Docebo_Users__c').update({ ...processedData, Id: existingDU.Id });
                            console.log(duUpd.success ? `   ✅ Docebo_Users__c updated: ${existingDU.Id}` : `   ⚠️ Docebo_Users__c update failed: ${JSON.stringify(duUpd.errors)}`);
                        } else {
                            const duCreate = await conn.sobject('Docebo_Users__c').create(processedData);
                            console.log(duCreate.success ? `   ✅ Docebo_Users__c created: ${duCreate.id}` : `   ⚠️ Docebo_Users__c creation failed: ${JSON.stringify(duCreate.errors)}`);
                        }
                    } catch (e) {
                        console.log(`   ❌ Docebo_Users__c ensure error: ${e.message || e}`);
                    }
                } else {
                    console.log(`   No existing Contact/Lead; creating Lead and Docebo_Users__c`);

                    // First ensure/create Docebo_Users__c
                    try {
                        const existingDU = await conn.sobject('Docebo_Users__c').findOne({ User_Unique_Id__c: processedData.User_Unique_Id__c });
                        if (existingDU) {
                            const duUpd = await conn.sobject('Docebo_Users__c').update({ ...processedData, Id: existingDU.Id });
                            console.log(duUpd.success ? `   ✅ Docebo_Users__c updated: ${existingDU.Id}` : `   ⚠️ Docebo_Users__c update failed: ${JSON.stringify(duUpd.errors)}`);
                        } else {
                            const duCreate = await conn.sobject('Docebo_Users__c').create(processedData);
                            console.log(duCreate.success ? `   Docebo_Users__c created: ${duCreate.id}` : `   Docebo_Users__c creation failed: ${JSON.stringify(duCreate.errors)}`);
                        }
                    } catch (e) {
                        console.log(`   ❌ Docebo_Users__c ensure error: ${e.message || e}`);
                    }

                    // Then attempt to create a new Lead (no Account creation)
                    try {
                        const leadCreate = await conn.sobject('Lead').create({
                            LastName: (processedData.Last_Name__c && processedData.Last_Name__c.trim() !== "") ? processedData.Last_Name__c : 'Unknown',
                            FirstName: processedData.First_Name__c || null,
                            Email: processedData.Email__c || null,
                            Company: processedData.Organization_Name__c || '-',
                            Title: processedData.Job_Title__c || '',
                            Website: processedData.Organization_URL__c || '',
                            Status: 'Open - Not Contacted',
                            Created_by_Docebo_API__c: true,
                            LeadSource: 'Docebo Platform'
                        });
                        if (leadCreate.success) {
                            console.log(`   Lead created: ${leadCreate.id}`);
                        } else {
                            console.log(`   Lead creation failed:`, leadCreate.errors);
                        }
                    } catch (e) {
                        console.log(`   Lead creation error: ${e.message || e}`);
                    }
                }

                successCount++;

            } catch (error) {
                console.error(`   ❌ Error processing user ${user.user_id}:`, error.message);
                errorCount++;
            }
        }
        
        console.log(`\nSummary: processed=${successCount}, errors=${errorCount}, total=${first5Users.length}`);
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Run the test
simple5UserTest().catch(console.error);
