const getConnection = require('../common/getConnection');

(async function main() {
  try {
    // Hardcoded input: contact email we want to associate to a Docebo_Users__c record
    const contactEmail = '<EMAIL>';

    const conn = await getConnection();
    if (!conn || !conn.accessToken) {
      console.error('❌ Could not get Salesforce connection.');
      process.exit(1);
    }

    console.log(`🔎 Looking up Contact by Email='${contactEmail}' ...`);
    const contact = await conn.sobject('Contact').findOne({ Email: contactEmail });
    if (!contact) {
      console.log(`⚠️ No Contact found with email: ${contactEmail}`);
      process.exit(0);
    }
    console.log(`✅ Found Contact: ${contact.Id}, AccountId=${contact.AccountId || 'null'}`);

    // Try to find matching Docebo_Users__c by Email__c first
    console.log(`🔎 Looking up Docebo_Users__c by Email__c='${contactEmail}' ...`);
    let du = await conn.sobject('Docebo_Users__c').findOne({ Email__c: contactEmail });

    // Fallback: try by Organization Email field fallback or by Contact.Id backlink
    if (!du) {
      console.log('ℹ️ No DU found by Email__c, trying by Contact__c backlink...');
      du = await conn.sobject('Docebo_Users__c').findOne({ Contact__c: contact.Id });
    }

    if (!du) {
      console.log('⚠️ No Docebo_Users__c record found matching the email/contact.');
      process.exit(0);
    }

    console.log(`✅ Found DU: ${du.Id}, Account__c=${du.Account__c || 'null'}, Contact__c=${du.Contact__c || 'null'}`);

    // If DU email differs from Contact email, align it to satisfy potential lookup filters
    if (du.Email__c !== contact.Email) {
      console.log(`ℹ️ Aligning DU.Email__c from '${du.Email__c || ''}' to '${contact.Email}' to satisfy lookup filters`);
      const emailUpd = await conn.sobject('Docebo_Users__c').update({ Id: du.Id, Email__c: contact.Email });
      if (!emailUpd.success) {
        console.warn('⚠️ Failed to update DU.Email__c:', emailUpd.errors);
      }
    }

    // Attempt association
    console.log(`🔗 Linking DU ${du.Id} -> Contact ${contact.Id} ...`);
    const linkRes = await conn.sobject('Docebo_Users__c').update({ Id: du.Id, Contact__c: contact.Id });
    if (linkRes.success) {
      console.log(`✅ Linked DU ${du.Id} to Contact ${contact.Id}`);
    } else {
      console.error('❌ Failed to link DU to Contact:', linkRes.errors);
      // Print helpful context
      console.log('Context:');
      console.log({ DU_Account__c: du.Account__c, Contact_AccountId: contact.AccountId, DU_Email__c: du.Email__c, Contact_Email: contact.Email });
      if (Array.isArray(linkRes.errors)) {
        const ffve = linkRes.errors.find(e => e && e.errorCode === 'FIELD_FILTER_VALIDATION_EXCEPTION');
        if (ffve) {
          console.log('ℹ️ Possible lookup filter preventing the link. Consider aligning Account or Email.');
        }
      }
      process.exit(1);
    }

    // Try setting Contact back-reference if field exists
    try {
      const contactDescribe = await conn.sobject('Contact').describe();
      const duLookup = contactDescribe.fields.find(f => f.name === 'Docebo_User__c');
      if (duLookup) {
        const backRef = await conn.sobject('Contact').update({ Id: contact.Id, Docebo_User__c: du.Id });
        if (backRef.success) {
          console.log(`✅ Set Contact.Docebo_User__c = ${du.Id}`);
        } else {
          console.warn('⚠️ Could not set Contact.Docebo_User__c:', backRef.errors);
        }
      } else {
        console.log('ℹ️ Contact.Docebo_User__c field not found; skipping back-reference');
      }
    } catch (e) {
      console.warn('⚠️ Error attempting Contact back-reference:', e && (e.message || e.toString()));
    }

    console.log('🎉 Done');
    process.exit(0);
  } catch (err) {
    console.error('❌ Unexpected error:', err && (err.message || err.toString()));
    process.exit(1);
  }
})();

