# 🔒 Using Your Existing SSL Setup

## 📋 Overview

Since you already have SSL configured at the server/vhost level, your Node.js application will run on HTTP internally, and your web server (Apache/Nginx) will handle SSL termination and proxy requests.

## 🏗️ Architecture

```
Internet → Apache/Nginx (Port 443 HTTPS) → Node.js App (Port 3000 HTTP)
```

Your setup:
- **Web Server**: Handles SSL certificates and HTTPS on port 443
- **Node.js App**: Runs HTTP only on port 3000
- **Proxy**: Web server forwards requests to your Node.js app

## 🚀 Starting Your Application

Simply run:
```bash
node app.js
```

Expected output:
```
Starting application in production mode...
Logs are being saved to daily log files in the logs directory
SSL is handled by your web server (Apache/Nginx vhost)
✅ HTTP Server running on port 3000
   Health check: http://localhost:3000/health
   External access via web server: https://communityreport.org
   Webhook endpoints available at:
   - https://communityreport.org/webhook/docebo/user/manage
   - https://communityreport.org/webhook/docebo/course/manage
   - https://communityreport.org/webhook/docebo/session/manage
   - https://communityreport.org/webhook/docebo/lp/manage
   - https://communityreport.org/webhook/docebo/course/completed
```

## 🔧 Web Server Configuration

You need to ensure your Apache/Nginx vhost is configured to proxy requests to your Node.js app on port 3000.

### Apache Configuration Example:
```apache
<VirtualHost *:443>
    ServerName communityreport.org
    
    # Your existing SSL configuration
    SSLEngine on
    SSLCertificateFile /path/to/your/cert.crt
    SSLCertificateKeyFile /path/to/your/private.key
    
    # Proxy to Node.js app
    ProxyPreserveHost On
    ProxyPass / http://localhost:3000/
    ProxyPassReverse / http://localhost:3000/
    
    # Enable proxy modules if not already enabled:
    # a2enmod proxy
    # a2enmod proxy_http
</VirtualHost>
```

### Nginx Configuration Example:
```nginx
server {
    listen 443 ssl;
    server_name communityreport.org;
    
    # Your existing SSL configuration
    ssl_certificate /path/to/your/cert.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # Proxy to Node.js app
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🔍 Testing Your Setup

### Test Internal HTTP Server:
```bash
# Test the Node.js app directly
curl http://localhost:3000/health

# Expected response:
# {"status":"healthy","timestamp":"...","environment":"production"}
```

### Test External HTTPS Access:
```bash
# Test through your web server (GET request - works)
curl https://communityreport.org/health

# Test webhook endpoints (POST request required - NOT GET!)
curl -X POST https://communityreport.org/webhook/docebo/course/manage \
  -H "Content-Type: application/json" \
  -d '{
    "message_id": "test-123",
    "event": "course.created",
    "payload": {
      "course_id": "12345",
      "course_name": "Test Course"
    }
  }'

# Expected response:
# {"status":"success","message":"Course webhook received, processing in background"}
```

### ⚠️ Important: Webhook Endpoints Only Accept POST Requests
If you try to access webhook endpoints with GET (like in a browser), you'll get:
```
Cannot GET /webhook/docebo/course/manage
```

This is **normal and expected** - webhooks are designed to receive POST requests from Docebo, not GET requests from browsers.

## 🚨 Troubleshooting

### Port 3000 Already in Use:
```bash
# Find what's using port 3000
sudo lsof -i :3000

# Kill the process if needed
sudo kill -9 <PID>

# Or change the port in your .env file:
PORT=3001
```

### Web Server Not Proxying:
```bash
# Check if your web server is running
sudo systemctl status apache2  # or nginx

# Check web server configuration
# For Apache:
sudo apache2ctl configtest

# For Nginx:
sudo nginx -t

# Restart web server after configuration changes
sudo systemctl restart apache2  # or nginx
```

### Application Not Starting:
```bash
# Check for errors
node app.js

# Check if dependencies are installed
npm install

# Check .env file exists and has correct settings
cat .env
```

## 📊 Process Management with PM2

For production, use PM2 to manage your Node.js process:

```bash
# Install PM2 globally
sudo npm install -g pm2

# Start your application
pm2 start app.js --name "docebo-salesforce"

# Configure PM2 to start on boot
pm2 startup
pm2 save

# Monitor your application
pm2 status
pm2 logs docebo-salesforce
pm2 monit
```

## 🔄 Deployment Workflow

1. **Update your code**:
   ```bash
   git pull origin main
   npm install
   ```

2. **Test locally**:
   ```bash
   curl http://localhost:3000/health
   ```

3. **Restart with PM2**:
   ```bash
   pm2 restart docebo-salesforce
   ```

4. **Test externally**:
   ```bash
   curl https://communityreport.org/health
   ```

## 📋 Configuration Summary

Your current configuration:
- **Node.js App**: HTTP on port 3000
- **Web Server**: HTTPS on port 443 (proxies to port 3000)
- **SSL**: Handled by your existing web server configuration
- **Webhooks**: Available at `https://communityreport.org/webhook/docebo/*`

This setup is perfect because:
- ✅ Your existing SSL certificates continue to work
- ✅ No need to manage SSL in Node.js
- ✅ Web server handles SSL termination efficiently
- ✅ Node.js app focuses on business logic
- ✅ Easy to scale and maintain

Your application is now ready to run with your existing SSL setup! 🎉
