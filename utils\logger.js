const fs = require('fs');
const path = require('path');

// Detect PM2 or disable flag
const runningUnderPM2 = !!(process.env.pm_id || process.env.PM2_HOME);
const forceFileLogs = process.env.PM2_FORCE_FILE_LOGS === '1' || process.env.DISABLE_FILE_LOGS === '0';
const disableFileLogs = (runningUnderPM2 && !forceFileLogs) || process.env.DISABLE_FILE_LOGS === '1';

// Preserve original console methods for fallback
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

// Ensure logs directory exists (when file logging is enabled)
const logsDir = path.join(__dirname, '../logs');
if (!disableFileLogs) {
    try {
        if (!fs.existsSync(logsDir)) {
            fs.mkdirSync(logsDir, { recursive: true });
        }
    } catch (e) {
        // If we cannot create logs dir, we will fall back to console output
        originalConsoleError('⚠️ Failed to ensure logs directory:', e && (e.message || e.toString()));
    }
}

/**
 * Gets the current log file path based on the current date
 * @returns {string} Path to the log file for today
 */
function getLogFilePath() {
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD format
    return path.join(logsDir, `${dateStr}.log`);
}

/**
 * Writes a log entry to the current day's log file
 * @param {string} level - Log level (info, error, etc.)
 * @param {Array} args - Arguments to log
 */
function writeToLogFile(level, ...args) {
    const now = new Date();
    // Format: YYYY-MM-DD HH:MM:SS.mmm
    const timestamp = now.toISOString().replace('T', ' ').replace('Z', '');
    // Add timezone info
    const timezoneOffset = now.getTimezoneOffset();
    const timezone = `UTC${timezoneOffset <= 0 ? '+' : '-'}${Math.abs(Math.floor(timezoneOffset / 60)).toString().padStart(2, '0')}:${Math.abs(timezoneOffset % 60).toString().padStart(2, '0')}`;
    
    const logFilePath = getLogFilePath();
    
    // Convert arguments to strings
    const logMessage = args.map(arg => {
        if (typeof arg === 'object') {
            return JSON.stringify(arg, null, 2);
        }
        return String(arg);
    }).join(' ');
    
    // Format the log entry with more detailed timestamp
    const logEntry = `[${timestamp} ${timezone}] [${level.toUpperCase()}] ${logMessage}\n`;
    
    // Append to the log file
    fs.appendFileSync(logFilePath, logEntry);
    
    // Also output to console if needed
    // console.log(`[${level.toUpperCase()}]`, ...args);
}

// Create logger object with methods for different log levels
const logger = {
    info: (...args) => {
        if (disableFileLogs) return originalConsoleLog(...args);
        try { writeToLogFile('info', ...args); } catch (e) { originalConsoleError('log write failed:', e); originalConsoleLog(...args); }
    },
    error: (...args) => {
        if (disableFileLogs) return originalConsoleError(...args);
        try { writeToLogFile('error', ...args); } catch (e) { originalConsoleError('log write failed:', e); originalConsoleError(...args); }
    },
    warn: (...args) => {
        if (disableFileLogs) return originalConsoleWarn(...args);
        try { writeToLogFile('warn', ...args); } catch (e) { originalConsoleError('log write failed:', e); originalConsoleWarn(...args); }
    },
    debug: (...args) => {
        if (disableFileLogs) return originalConsoleLog(...args);
        try { writeToLogFile('debug', ...args); } catch (e) { originalConsoleError('log write failed:', e); originalConsoleLog(...args); }
    }
};

// If file logging is enabled, override console.* to use our logger; otherwise keep defaults so PM2 captures them
if (!disableFileLogs) {
    console.log = (...args) => logger.info(...args);
    console.error = (...args) => logger.error(...args);
    console.warn = (...args) => logger.warn(...args);
}

module.exports = logger;