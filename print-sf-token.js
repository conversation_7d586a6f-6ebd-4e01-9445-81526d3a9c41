#!/usr/bin/env node

require('dotenv').config();
const getSFToken = require('./platform/salesforce/common/getAccessToken');

function mask(str, start = 8, end = 6) {
  if (!str || typeof str !== 'string') return String(str);
  if (str.length <= start + end) return '*'.repeat(str.length);
  return `${str.slice(0, start)}...${str.slice(-end)}`;
}

async function main() {
  try {
    const showFull = process.argv.includes('--full');
    const jsonOnly = process.argv.includes('--json-only');

    if (!jsonOnly) {
      console.log('🔎 Salesforce OAuth (client_credentials) token fetch');
      console.log('- SF_TOKEN_URL:', process.env.SF_TOKEN_URL || '(missing)');
      console.log('- SF_API_GRANT_TYPE:', process.env.SF_API_GRANT_TYPE || '(missing)');
    }

    const tokenRes = await getSFToken();
    if (tokenRes.status !== 200) {
      console.error('❌ Failed to obtain token');
      console.error('   Details:', tokenRes.message || tokenRes);
      process.exit(1);
    }

    const { accessToken, instanceUrl } = tokenRes.data;
    const output = {
      instanceUrl,
      accessToken: showFull ? accessToken : mask(accessToken),
      accessTokenLength: accessToken?.length || 0,
      masked: !showFull
    };

    if (jsonOnly) {
      // Print only JSON (suitable for piping into tools)
      process.stdout.write(JSON.stringify(output));
      return;
    }

    console.log('✅ Token obtained:');
    console.log(JSON.stringify(output, null, 2));

    if (!showFull) {
      console.log('\nℹ️ Run with --full to print the full token (be cautious; this is sensitive).');
    }
  } catch (err) {
    console.error('💥 Unexpected error:', err.message);
    process.exit(1);
  }
}

main();

