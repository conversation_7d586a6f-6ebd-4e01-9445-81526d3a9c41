const axios = require('axios');
require('dotenv').config();

const TOKEN_URL = process.env.SF_TOKEN_URL
const CLIENT_ID = process.env.SF_API_CLIENT_ID
const CLIENT_SECRET = process.env.SF_API_CLIENT_SECRET
const GRANT_TYPE = process.env.SF_API_GRANT_TYPE
const USER_NAME = process.env.SF_API_USER_NAME
const PASSWORD = process.env.SF_API_PASSWORD

let accessToken = "";
let tokenExpiry = 0; // Track when token expires

module.exports = async function getAccessToken() {
    try {
        // Check if we have a valid token that hasn't expired
        const now = Date.now();
        if (accessToken && tokenExpiry > now + 60000) { // 1 minute buffer
            return {
                status: 200,
                data: {
                    accessToken: accessToken,
                    instanceUrl: instanceUrl
                }
            };
        }

        console.log('🔄 Refreshing Salesforce access token...');

        // Build request parameters based on grant type
        const params = {
            grant_type: GRANT_TYPE,
            client_id: CLIENT_ID,
            client_secret: CLIENT_SECRET
        };

        // Only add username/password for password grant type
        if (GRANT_TYPE === 'password') {
            params.username = USER_NAME;
            params.password = PASSWORD;
        }

        const response = await axios.post(
            TOKEN_URL,
            new URLSearchParams(params),
            {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
            }
        );

        accessToken = response.data.access_token;
        instanceUrl = response.data.instance_url;

        // Set token expiry (default 2 hours for client_credentials)
        const expiresIn = response.data.expires_in || 7200; // 2 hours default
        tokenExpiry = Date.now() + (expiresIn * 1000);

        console.log(`✅ Token refreshed, expires in ${Math.round(expiresIn/60)} minutes`);
        if (accessToken != "") {
            return {
                status: 200,
                data: {
                    accessToken: accessToken,
                    instanceUrl: instanceUrl
                }
            }
        } else {
            return {
                status: 203,
                accessToken: "No token"
            }
        }
    } catch (error) {
        return {
            status: 505,
            message: error.response?.data || error.message
        }
    }
}
