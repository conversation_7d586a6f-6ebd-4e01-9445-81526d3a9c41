#!/usr/bin/env node

require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function main() {
  const duId = process.argv[2];
  const leadId = process.argv[3];
  if (!duId || !leadId) {
    console.log('Usage: node link-du-to-lead.js <DOCEBO_USER_ID> <LEAD_ID>');
    process.exit(1);
  }

  const conn = await getConnection();
  if (!conn || !conn.accessToken) {
    console.error('❌ Invalid Salesforce connection');
    process.exit(1);
  }

  try {
    // Fetch Lead to get canonical email
    const lead = await conn.sobject('Lead').findOne({ Id: leadId });
    if (!lead) {
      console.error('❌ Lead not found:', leadId);
      process.exit(1);
    }
    const leadEmail = lead.Email || null;

    // Fetch DU to compare
    const du = await conn.sobject('Docebo_Users__c').findOne({ Id: duId });
    if (!du) {
      console.error('❌ Docebo_Users__c not found:', duId);
      process.exit(1);
    }

    // Normalize Email__c to match Lead.Email if different (helps pass lookup filter if any)
    let updatePayload = { Id: duId, Lead__c: leadId };
    if ((du.Email__c || '') !== (leadEmail || '')) {
      updatePayload.Email__c = leadEmail || du.Email__c;
    }

    console.log('🔗 Attempting to link DU → Lead with payload:', updatePayload);
    const linkRes = await conn.sobject('Docebo_Users__c').update(updatePayload);
    if (linkRes.success) {
      console.log('✅ Linked Docebo_Users__c to Lead successfully');
    } else {
      console.error('❌ Link failed:', linkRes.errors);
    }

    // Optionally set back-reference on Lead if field exists
    try {
      const leadDescribe = await conn.sobject('Lead').describe();
      const duLookup = leadDescribe.fields.find(f => f.name === 'Docebo_User__c');
      if (duLookup) {
        const backRefRes = await conn.sobject('Lead').update({ Id: leadId, Docebo_User__c: duId });
        if (backRefRes.success) {
          console.log('🔁 Lead back-reference set: Docebo_User__c');
        } else {
          console.log('ℹ️ Back-reference update failed:', backRefRes.errors);
        }
      } else {
        console.log('ℹ️ Lead.Docebo_User__c field not present; skipping back-reference');
      }
    } catch (e) {
      console.log('ℹ️ Could not set back-reference:', e.message || e);
    }
  } catch (e) {
    console.error('💥 Unexpected error:', e.message || e);
    process.exit(1);
  }
}

main();

