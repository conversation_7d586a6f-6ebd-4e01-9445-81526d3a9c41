#!/usr/bin/env node

/**
 * Test Session Sync for Specific Course
 *
 * This script tests the session sync process for a specific course to verify everything works
 */

// Toggle file logging here (default: enabled). Set to false to disable.
const ENABLE_FILE_LOG = true;
if (ENABLE_FILE_LOG && !process.env.SMALL_SYNC_FILE_LOG && !process.env.HISTORICAL_FILE_LOG) {
  process.env.SMALL_SYNC_FILE_LOG = '1';
}

require('dotenv').config();

// Optional file logging for test runs: set SMALL_SYNC_FILE_LOG=1 or HISTORICAL_FILE_LOG=1 to enable
const fs = require('fs');
const path = require('path');
(function setupFileLogger() {
  try {
    if (process.env.SMALL_SYNC_FILE_LOG === '1' || process.env.HISTORICAL_FILE_LOG === '1') {
      const logsDir = path.join(process.cwd(), 'logs');
      if (!fs.existsSync(logsDir)) fs.mkdirSync(logsDir, { recursive: true });
      const ts = new Date().toISOString().replace(/[:.]/g, '-');
      const logPath = path.join(logsDir, `test-session-sync-${ts}.log`);
      const stream = fs.createWriteStream(logPath, { flags: 'a' });

      const origLog = console.log.bind(console);
      const origWarn = console.warn.bind(console);
      const origError = console.error.bind(console);

      const safe = (v) => {
        if (v instanceof Error) return `${v.name}: ${v.message}`;
        if (typeof v === 'string') return v;
        try { return JSON.stringify(v); } catch { return String(v); }
      };
      const stamp = (lvl, args) => `[${new Date().toISOString()}] [${lvl}] ${args.map(safe).join(' ')}\n`;

      console.log = (...args) => { try { stream.write(stamp('INFO', args)); } catch {} origLog(...args); };
      console.warn = (...args) => { try { stream.write(stamp('WARN', args)); } catch {} origWarn(...args); };
      console.error = (...args) => { try { stream.write(stamp('ERROR', args)); } catch {} origError(...args); };

      origLog(`📝 Test-session-sync file logging enabled: ${logPath}`);
    }
  } catch (e) {
    console.error('File logging setup failed:', e);
  }
})();

const doceboService = require("./platform/docebo/services");
const { createNewSession } = require("./platform/salesforce/session/createSession");
const getConnection = require('./platform/salesforce/common/getConnection');

async function testSessionSync(courseId) {
    try {
        console.log(`🧪 TESTING SESSION SYNC FOR COURSE ${courseId}`);
        console.log('=' .repeat(60));
        console.log('This process will:');
        console.log('1. Fetch course information from Docebo');
        console.log('2. Get all sessions for the specified course');
        console.log('3. For each session, get detailed session information');
        console.log('4. Create/update Docebo_Session__c record in Salesforce');
        console.log('5. Map all session fields properly');
        console.log('');
        console.log('🎯 This is a TEST RUN to verify the session sync process works');
        console.log('');

        const startTime = Date.now();

        // Step 1: Test Salesforce connection
        console.log('🔐 Testing Salesforce connection...');
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return false;
        }
        console.log('✅ Salesforce connection established');

        // Step 2: Get course information
        console.log(`📚 Fetching course information for course ${courseId}...`);
        const courseInfo = await doceboService.getCourseInfo(courseId);
        
        if (!courseInfo || courseInfo.status !== 200) {
            console.error(`❌ Failed to fetch course info: ${courseInfo?.status || 'Unknown error'}`);
            return false;
        }

        console.log(`✅ Course found: ${courseInfo.data.name}`);
        console.log(`   Type: ${courseInfo.data.type}`);
        console.log(`   Status: ${courseInfo.data.status}`);

        // Step 3: Get sessions for this course
        console.log(`📋 Fetching sessions for course ${courseId}...`);
        const sessionListedInfo = await doceboService.getSessionListedInfo(courseId);
        
        if (!sessionListedInfo || sessionListedInfo.length === 0) {
            console.log('⚠️ No sessions found for this course');
            console.log('✅ Test completed - course has no sessions to sync');
            return true;
        }

        console.log(`✅ Found ${sessionListedInfo.length} sessions for this course`);

        let successCount = 0;
        let errorCount = 0;

        // Step 4: Process each session
        for (let i = 0; i < sessionListedInfo.length; i++) {
            const session = sessionListedInfo[i];
            const sessionId = session.id;
            
            console.log(`\n📅 Processing session ${i + 1}/${sessionListedInfo.length}: ${session.name || 'Unnamed Session'} (ID: ${sessionId})`);
            
            try {
                // Get detailed session information
                console.log(`   📋 Fetching detailed info for session ${sessionId}...`);
                const sessionInfo = await doceboService.getCourseSessionInfo(sessionId);
                
                if (sessionInfo.status !== 200) {
                    console.error(`   ❌ Failed to fetch session info: ${sessionInfo.status}`);
                    errorCount++;
                    continue;
                }

                console.log(`   ✅ Session info fetched successfully`);
                console.log(`   📝 Session: ${sessionInfo.data.name || 'Unnamed Session'}`);
                console.log(`   📅 Start: ${sessionInfo.data.date_start || 'Not set'}`);
                console.log(`   📅 End: ${sessionInfo.data.date_end || 'Not set'}`);
                console.log(`   👥 Max Enrollments: ${sessionInfo.data.max_enroll || 'Unlimited'}`);

                // Show raw session data for debugging
                console.log(`\n   📋 RAW SESSION DATA:`);
                console.log(`   ${'='.repeat(40)}`);
                console.log(JSON.stringify(sessionInfo.data, null, 2));
                console.log(`   ${'='.repeat(40)}\n`);

                // Create/update session in Salesforce
                console.log(`   💾 Creating/updating session in Salesforce...`);
                const sessionSaveRes = await createNewSession(sessionInfo.data, courseInfo.data);
                
                if (sessionSaveRes) {
                    console.log(`   ✅ Session saved successfully in Salesforce`);
                    successCount++;
                } else {
                    console.error(`   ❌ Failed to save session in Salesforce`);
                    errorCount++;
                }

            } catch (error) {
                console.error(`   ❌ Error processing session ${sessionId}:`, error.message);
                errorCount++;
            }
        }

        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);

        console.log('\n' + '='.repeat(60));
        console.log('🎉 SESSION SYNC TEST COMPLETED!');
        console.log(`⏱️  Total time: ${duration} seconds`);
        console.log(`✅ Successful: ${successCount} sessions`);
        console.log(`❌ Errors: ${errorCount} sessions`);
        console.log(`📊 Success rate: ${Math.round((successCount / sessionListedInfo.length) * 100)}%`);
        console.log('');
        
        if (successCount > 0) {
            console.log('✅ Test completed successfully with session sync:');
            console.log('   • Session details fetched from Docebo API');
            console.log('   • Docebo_Session__c records created/updated in Salesforce');
            console.log('   • All session fields mapped properly');
            console.log('');
            console.log('🎯 If this test looks good, you can run the full session sync!');
        } else {
            console.log('⚠️ No sessions were successfully synced. Please check the errors above.');
        }

        return successCount > 0;

    } catch (error) {
        console.error('❌ Session sync test failed:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Parse command line arguments
const courseId = parseInt(process.argv[2]);

if (!courseId) {
    console.error('❌ Please provide a course ID as an argument');
    console.error('Usage: node test-session-sync.js <courseId>');
    console.error('Example: node test-session-sync.js 3');
    process.exit(1);
}

// Run the test
testSessionSync(courseId).catch(console.error);
