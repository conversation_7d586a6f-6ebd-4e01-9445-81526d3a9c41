#!/usr/bin/env node

require('dotenv').config();
const axios = require('axios');
const getSFToken = require('./platform/salesforce/common/getAccessToken');

async function main() {
  try {
    console.log('🔐 Fetching Salesforce token via client_credentials...');
    const tokenRes = await getSFToken();
    if (tokenRes.status !== 200) {
      console.error('❌ Failed to get token:', tokenRes);
      process.exit(1);
    }
    const { accessToken, instanceUrl } = tokenRes.data;
    console.log('✅ Token OK');
    console.log('   Instance:', instanceUrl);

    // Try to describe Lead (to check access)
    try {
      await axios.get(`${instanceUrl}/services/data/v60.0/sobjects/Lead/describe`, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });
      console.log('ℹ️ Lead describe succeeded (Lead visible).');
    } catch (e) {
      const msg = e.response?.data || e.message;
      console.log('ℹ️ Lead describe failed (likely no access):', JSON.stringify(msg));
    }

    // Attempt to create a simple Lead
    const email = `apitest+${Date.now()}@example.com`;
    const payload = {
      LastName: 'API Test',
      FirstName: 'Docebo',
      Company: '-',
      Email: email,
      Status: 'Open - Not Contacted',
      LeadSource: 'Docebo Platform',
      Created_by_Docebo_API__c: true
    };

    console.log('🧪 Creating Lead with payload:', payload);
    try {
      const res = await axios.post(`${instanceUrl}/services/data/v60.0/sobjects/Lead`, payload, {
        headers: { Authorization: `Bearer ${accessToken}`, 'Content-Type': 'application/json' }
      });
      console.log('✅ Lead create response:', res.data);
      process.exit(0);
    } catch (e) {
      const status = e.response?.status;
      const data = e.response?.data;
      console.error('❌ Lead create failed');
      if (status) console.error('   HTTP status:', status);
      if (data) console.error('   Response data:', JSON.stringify(data));
      console.error('   Message:', e.message);
      process.exit(1);
    }
  } catch (err) {
    console.error('💥 Unexpected error:', err.message);
    process.exit(1);
  }
}

main();

