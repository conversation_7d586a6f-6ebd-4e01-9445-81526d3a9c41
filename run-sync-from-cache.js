#!/usr/bin/env node

/**
 * Run Sync From Cache
 * 
 * This script runs the user sync using cached Docebo data instead of hitting the API
 */

require('dotenv').config();
const fs = require('fs').promises;
const getConnection = require('./platform/salesforce/common/getConnection');
const { tidyData } = require('./platform/salesforce/users/createUser');

const CACHE_FILE = './data/docebo-users-cache.json';

async function runSyncFromCache(userLimit = null) {
    try {
        console.log('🚀 RUNNING SYNC FROM CACHED DATA');
        console.log('=' .repeat(50));
        
        if (userLimit) {
            console.log(`🧪 TEST MODE: Processing only ${userLimit} users`);
        }
        
        // Step 1: Load cached data
        console.log('📁 Loading cached Docebo users...');
        
        let cacheData;
        try {
            const cacheContent = await fs.readFile(CACHE_FILE, 'utf8');
            cacheData = JSON.parse(cacheContent);
        } catch (error) {
            console.error('❌ Failed to load cache file:', error.message);
            console.log('💡 Run "node cache-docebo-users.js" first to create the cache');
            return false;
        }
        
        console.log(`✅ Loaded ${cacheData.totalUsers} users from cache`);
        console.log(`📅 Cache created: ${cacheData.cachedAt}`);
        
        // Step 2: Get Salesforce connection
        console.log('\n🔐 Getting Salesforce connection...');
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return false;
        }
        console.log('✅ Salesforce connection established');
        
        // Step 3: Process users
        let usersToProcess = cacheData.users;
        
        // Limit users if specified (for testing)
        if (userLimit && userLimit > 0) {
            usersToProcess = usersToProcess.slice(0, userLimit);
            console.log(`🧪 Limited to ${usersToProcess.length} users for testing`);
        }
        
        console.log(`\n👥 Processing ${usersToProcess.length} users...`);
        
        let successCount = 0;
        let errorCount = 0;
        let createdLeads = 0;
        let updatedContacts = 0;
        let updatedLeads = 0;
        
        for (let i = 0; i < usersToProcess.length; i++) {
            const cachedUser = usersToProcess[i];
            const user = cachedUser.detailedInfo;
            
            console.log(`\n👤 Processing user ${i + 1}/${usersToProcess.length}: ${user.user_data?.first_name} ${user.user_data?.last_name} (ID: ${user.user_data?.user_id})`);
            
            try {
                // Process the user data
                const processedData = tidyData(user);
                console.log(`   🔧 Processed user data:`, {
                    name: processedData.Full_Name__c,
                    email: processedData.Email__c,
                    jobTitle: processedData.Job_Title__c,
                    organization: processedData.Organization_Name__c
                });
                
                // Check if user exists in Salesforce
                const email = processedData.Email__c;
                if (!email) {
                    console.log(`   ⚠️ No email for user ${user.user_data?.user_id}, skipping`);
                    continue;
                }
                
                // Search for existing Contact
                const contactQuery = `SELECT Id, Email FROM Contact WHERE Email = '${email}' LIMIT 1`;
                const contactResult = await conn.query(contactQuery);
                
                // Search for existing Lead
                const leadQuery = `SELECT Id, Email FROM Lead WHERE Email = '${email}' AND IsConverted = false LIMIT 1`;
                const leadResult = await conn.query(leadQuery);
                
                if (contactResult.records.length > 0) {
                    console.log(`   ✅ Found existing Contact: ${contactResult.records[0].Id}`);
                    // TODO: Update Contact with new fields
                    updatedContacts++;
                } else if (leadResult.records.length > 0) {
                    console.log(`   ✅ Found existing Lead: ${leadResult.records[0].Id}`);
                    // TODO: Update Lead with new fields
                    updatedLeads++;
                } else {
                    console.log(`   🆕 User not found in Salesforce - would create new Lead`);
                    // TODO: Create new Lead
                    createdLeads++;
                }
                
                successCount++;
                
            } catch (error) {
                console.error(`   ❌ Error processing user ${user.user_data?.user_id}:`, error.message);
                errorCount++;
            }
        }
        
        console.log('\n📊 SYNC FROM CACHE SUMMARY:');
        console.log(`✅ Successfully processed: ${successCount} users`);
        console.log(`❌ Errors: ${errorCount} users`);
        console.log(`🆕 Would create Leads: ${createdLeads}`);
        console.log(`📝 Would update Contacts: ${updatedContacts}`);
        console.log(`📝 Would update Leads: ${updatedLeads}`);
        console.log(`📥 Total users processed: ${usersToProcess.length}`);
        
        if (successCount === usersToProcess.length) {
            console.log('\n🎉 ALL USERS PROCESSED SUCCESSFULLY!');
            console.log('💡 This was a dry run. Implement actual Lead/Contact creation/updates as needed.');
        } else {
            console.log('\n⚠️ Some users failed processing. Check the errors above.');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Sync from cache failed:', error.message);
        console.error('Stack trace:', error.stack);
        return false;
    }
}

// Parse command line arguments
const args = process.argv.slice(2);
const userLimit = args.length > 0 ? parseInt(args[0]) : null;

if (userLimit && isNaN(userLimit)) {
    console.error('❌ Invalid user limit. Please provide a number or no argument for all users.');
    process.exit(1);
}

// Run the sync
runSyncFromCache(userLimit)
    .then(success => {
        if (success) {
            console.log('\n✅ Sync from cache completed.');
            process.exit(0);
        } else {
            console.log('\n❌ Sync from cache failed.');
            process.exit(1);
        }
    })
    .catch(err => {
        console.error('💥 Sync from cache process failed:', err);
        process.exit(1);
    });
