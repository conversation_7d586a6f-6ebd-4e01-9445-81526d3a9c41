const getAccessToken = require("./getAccessToken");
const jsforce = require('jsforce');

module.exports = async function getConnection() {
    try {
        let crendentialResult = await getAccessToken();
        //console.log("This is getConnection function", crendentialResult);
        
        if (crendentialResult.status == 200) {
            const conn = new jsforce.Connection({
                instanceUrl: crendentialResult.data.instanceUrl,
                accessToken: crendentialResult.data.accessToken,
                version: '60.0'
            });
            return conn;
        } else {
            console.error("Failed to get Salesforce access token:", crendentialResult.message);
            // Return a null connection object with error information
            return {
                accessToken: null,
                error: crendentialResult.message,
                status: crendentialResult.status
            };
        }
    } catch (error) {
        console.error("Error in getConnection:", error);
        // Return a null connection object with error information
        return {
            accessToken: null,
            error: error.message,
            status: 500
        };
    }
}