require('dotenv').config();
const getApiData = require("../../common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;
// const fs = require('fs');

async function getUserInfo(userId) {
    let userInfo = await getApiData('GET', `${APP_BASE}/manage/v1/user/${userId}`, null);
    return userInfo;
}

async function getEnrolledInfo(courseId, userId) {
    let enrolledInfo = await getApiData('GET', `${APP_BASE}/learn/v1/enrollments/${courseId}/${userId}`, null);
    return enrolledInfo;
}

async function getCourseInfo(courseId) {
    let courseInfo = await getApiData('GET', `${APP_BASE}/course/v1/courses/${courseId}`, null);
    return courseInfo;
}

async function getCourseSessionInfo(sessionId) {
    let sessionInfo = await getApiData('GET', `${APP_BASE}/course/v1/sessions/${sessionId}`, null);
    return sessionInfo;
}

async function getLearningPlan(lpId) {
    let lpInfo = await getApiData('GET', `${APP_BASE}/learn/v1/lp/${lpId}`, null);
    return lpInfo;
}

async function getInstructorData(userId, courseId, sessionId = null) {
    const params = {
        user_id: userId,
        course_id: courseId
    };

    if (sessionId) {
        params.session_id = sessionId;
    }

    let instructorInfo = await getApiData('GET', `${APP_BASE}/learn/v1/instructor/getInstructorData`, params);
    return instructorInfo;
}

async function getCourseCategories() {
    try {
        console.log('📂 Fetching course categories from Docebo...');
        let categoriesInfo = await getApiData('GET', `${APP_BASE}/course/v1/categories`, null);
        return categoriesInfo;
    } catch (error) {
        console.error('Error fetching course categories:', error);
        return { status: 505, data: error.message };
    }
}

async function getAllCourseCategories() {
    try {
        console.log('📂 Fetching all course categories with pagination...');
        let page = 1;
        const pageSize = 200;
        let allCategories = [];
        let hasMoreData = true;

        while (hasMoreData) {
            const response = await getApiData(
                'GET',
                `${APP_BASE}/course/v1/categories?page=${page}&page_size=${pageSize}`,
                null
            );

            if (!response || response.status !== 200) {
                console.error(`Failed to fetch categories on page ${page}`);
                break;
            }

            const categories = response.data?.items || response.data || [];

            if (categories.length === 0) {
                hasMoreData = false;
                break;
            }

            console.log(`   📋 Page ${page}: Found ${categories.length} categories`);
            allCategories.push(...categories);

            // Check if there's more data
            hasMoreData = response.data?.has_more_data || categories.length === pageSize;
            page++;
        }

        console.log(`✅ Total categories found: ${allCategories.length}`);
        return {
            status: 200,
            data: allCategories
        };

    } catch (error) {
        console.error('Error fetching all course categories:', error);
        return { status: 505, data: error.message };
    }
}

async function getUserListedInfo(userId) {
    try {
        // Check if the Docebo API has a direct endpoint to get user by ID
        // If available, use it instead of fetching all users
        const directResponse = await getApiData('GET', `${APP_BASE}/manage/v1/user/${userId}`, null);
        
        // If we got a direct response with the user, return it
        if (directResponse && directResponse.data && !directResponse.data.error) {
            return directResponse.data;
        }
        
        // If direct endpoint failed or isn't available, use a filtered query
        // Some APIs support filter parameters - check if Docebo does
        const filteredResponse = await getApiData(
            'GET',
            `${APP_BASE}/manage/v1/user?user_id=${userId}&page=1&page_size=1`,
            null
        );
        
        if (filteredResponse &&
            filteredResponse.data &&
            filteredResponse.data.items &&
            filteredResponse.data.items.length > 0) {
            return filteredResponse.data.items[0];
        }
        
        // If filtered query also failed, fall back to the original approach
        // but with optimizations
        console.log(`Warning: Using inefficient method to find user ${userId} - consider API optimization`);
        
        let page = 1;
        let pageSize = 200; // Reduced page size for better response times
        let maxPages = 10;  // Set a reasonable limit to prevent infinite loops
        
        while (page <= maxPages) {
            const response = await getApiData(
                'GET',
                `${APP_BASE}/manage/v1/user?page=${page}&page_size=${pageSize}`,
                null
            );
            
            if (!response || !response.data || !response.data.items) {
                console.error("Invalid response from Docebo API");
                break;
            }
            
            // Check if user exists in current page
            const user = response.data.items.find(item => String(item.user_id) === String(userId));
            if (user) {
                return user;
            }
            
            // If no more data or reached max pages, stop
            if (!response.data.has_more_data || page >= maxPages) {
                break;
            }
            
            page++;
        }
        
        console.error(`User ${userId} not found after checking ${maxPages} pages`);
        return null;
    } catch (error) {
        console.error(`Error fetching user info for ID ${userId}:`, error);
        return null;
    }
}

// async function getTotalUserListedInfo() {
//     let page = 1;
//     let pageSize = 5000;
//     let userListedInfo = [];
//     let hasMoreData = true;
//     // const filePath = 'output.txt';
//     while (hasMoreData) {
//         let response = await getApiData('GET', `${APP_BASE}/manage/v1/user?page=${page}&page_size=${pageSize}`, null);
//         let items = response.data.items;
//         if(items.length > 0) {
//             userListedInfo.push(...items);
//         }
//         if (response.data.has_more_data) {
//             page++;
//         } else {
//             hasMoreData = false;
//         }
//     }
//     return userListedInfo;
// }

async function getTotalUserListedInfo(limit = null) {
    let page = 1;
    const pageSize = 200; // Safer default, as 5000 might be too large for Docebo API
    let userListedInfo = [];

    while (true) {
        const response = await getApiData(
            'GET',
            `${APP_BASE}/manage/v1/user?page=${page}&page_size=${pageSize}`,
            null
        );

        const items = response?.data?.items || [];
        if (items.length === 0) break; // No more data

        if (limit && limit > 0) {
            // Push only up to remaining needed
            const remaining = limit - userListedInfo.length;
            userListedInfo.push(...items.slice(0, Math.max(0, remaining)));
            console.log(`Fetched page ${page} (${Math.min(items.length, remaining)} users; total so far ${userListedInfo.length}/${limit})`);
            if (userListedInfo.length >= limit) break; // Reached the desired count
        } else {
            userListedInfo.push(...items);
            console.log(`Fetched page ${page} (${items.length} users)`);
        }

        if (items.length < pageSize) break; // Last page
        page++;
    }

    return userListedInfo;
}

async function getCourseListedInfo(courseId) {
    let page = 1;
    let pageSize = 300;
    let courseListedInfo = null;
    let hasMoreData = true;
    while (hasMoreData) {
        let response = await getApiData('GET', `${APP_BASE}/course/v1/courses?page=${page}&page_size=${pageSize}`, null);
        let items = response.data.items;
        for (let item of items) {
            if (item.id === courseId) {
                courseListedInfo = item;
                hasMoreData = false;
                break;
            }
        }
        if (response.data.has_more_data) {
            page++;
        } else {
            hasMoreData = false;
        }
    }
    return courseListedInfo;
}

async function getTotalCourseListedInfo() {
    let page = 1;
    let pageSize = 200;
    let courseListedInfo = [];
    let hasMoreData = true;
    // const filePath = 'output.txt';
    while (hasMoreData) {
        let response = await getApiData('GET', `${APP_BASE}/course/v1/courses?page=${page}&page_size=${pageSize}`, null);
        let items = response.data.items;
        if (items.length > 0) {
            courseListedInfo.push(...items);
        }
        if (response.data.has_more_data) {
            page++;
        } else {
            hasMoreData = false;
        }
    }
    return courseListedInfo;
}

async function getSessionListedInfo(courseId) {
    let sessionListedInfo = [];
    let response = await getApiData('GET', `${APP_BASE}/learn/v1/courses/${courseId}/sessions`, null);
    if (response.data.sessions) {
        sessionListedInfo = [...response.data.sessions];        
    }
    return sessionListedInfo;
}

async function getTotalLearningPlanListedInfo() {
    let page = 1;
    let pageSize = 10;
    let learningPlanListedInfo = [];
    let hasMoreData = true;
    // const filePath = 'output.txt';
    while (hasMoreData) {
        let response = await getApiData('GET', `${APP_BASE}/learningplan/v1/learningplans?page=${page}&page_size=${pageSize}`, null);
        let items = response.data.items;
        if (items.length > 0) {
            learningPlanListedInfo.push(...items);
        }
        if (response.data.has_more_data) {
            page++;
        } else {
            hasMoreData = false;
        }
    }
    return learningPlanListedInfo;
}

async function getTotalLearningPlanEnrollmentListedInfo() {
    let page = 1;
    let pageSize = 10;
    let learningPlanEnrollmentListedInfo = [];
    let hasMoreData = true;
    while (hasMoreData) {
        let response = await getApiData('GET', `${APP_BASE}/learningplan/v1/learningplans/enrollments?page=${page}&page_size=${pageSize}`, null);
        let items = response.data.items;
        if (Array(items) && items.length > 0) {
            items.forEach(item => {
                console.log(item.user_id, item.learning_plan_id);
                learningPlanEnrollmentListedInfo.push({user_id: item.user_id, learning_plan_id: item.learning_plan_id});
            });
        }
        if (response.data.has_more_data) {
            page++;
        } else {
            hasMoreData = false;
        }
    }
    return learningPlanEnrollmentListedInfo;
}


module.exports = {
    getUserInfo,
    getEnrolledInfo,
    getCourseInfo,
    getCourseSessionInfo,
    getLearningPlan,
    getInstructorData,
    getUserListedInfo,
    getCourseListedInfo,
    getTotalUserListedInfo,
    getTotalCourseListedInfo,
    getSessionListedInfo,
    getTotalLearningPlanListedInfo,
    getTotalLearningPlanEnrollmentListedInfo
}
