#!/usr/bin/env node

/**
 * Test Categories API
 * 
 * This script tests the Docebo categories API to see what data is available
 */

require('dotenv').config();
const doceboService = require("./platform/docebo/services");

async function testCategoriesAPI() {
    try {
        console.log('🧪 TESTING DOCEBO CATEGORIES API');
        console.log('=' .repeat(50));
        
        // Test basic categories endpoint
        console.log('📂 Testing getCourseCategories()...');
        const basicCategories = await doceboService.getCourseCategories();
        console.log('Basic categories response:', JSON.stringify(basicCategories, null, 2));
        
        console.log('\n📂 Testing getAllCourseCategories()...');
        const allCategories = await doceboService.getAllCourseCategories();
        console.log('All categories response:', JSON.stringify(allCategories, null, 2));
        
        // If we got data, look for category ID 32
        if (allCategories.status === 200 && allCategories.data) {
            console.log('\n🔍 Looking for category ID 32...');
            const category32 = allCategories.data.find(cat => cat.id === 32);
            if (category32) {
                console.log('Found category 32:', JSON.stringify(category32, null, 2));
            } else {
                console.log('Category 32 not found in results');
            }
        }
        
    } catch (error) {
        console.error('❌ Error testing categories API:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

// Run the test
testCategoriesAPI().catch(console.error);
