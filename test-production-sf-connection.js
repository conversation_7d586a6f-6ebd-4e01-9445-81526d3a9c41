#!/usr/bin/env node

/**
 * Test Production Salesforce Connection
 * 
 * Run this before the historical sync to ensure your production
 * Salesforce credentials are working correctly.
 */

require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function testProductionConnection() {
    console.log('🔍 Testing Production Salesforce Connection...');
    console.log('=' .repeat(50));
    
    // Show current configuration (without sensitive data)
    console.log('📋 Current Configuration:');
    console.log(`   Token URL: ${process.env.SF_TOKEN_URL}`);
    console.log(`   Instance: ${process.env.SF_API_INSTANCE}`);
    console.log(`   Username: ${process.env.SF_API_USER_NAME}`);
    console.log(`   Client ID: ${process.env.SF_API_CLIENT_ID ? process.env.SF_API_CLIENT_ID.substring(0, 10) + '...' : 'NOT SET'}`);
    console.log(`   Client Secret: ${process.env.SF_API_CLIENT_SECRET ? '***SET***' : 'NOT SET'}`);
    console.log(`   Password: ${process.env.SF_API_PASSWORD ? '***SET***' : 'NOT SET'}`);
    console.log('');
    
    try {
        console.log('🔐 Attempting to connect to Salesforce...');
        const conn = await getConnection();
        
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to get valid connection or access token');
        }
        
        console.log('✅ Connection successful!');
        console.log(`   Access Token: ${conn.accessToken.substring(0, 20)}...`);
        console.log(`   Instance URL: ${conn.instanceUrl}`);
        console.log('');
        
        // Test a simple query
        console.log('🔍 Testing query capabilities...');
        const result = await conn.query("SELECT Id, Name FROM Organization LIMIT 1");
        
        if (result.records && result.records.length > 0) {
            console.log('✅ Query test successful!');
            console.log(`   Organization: ${result.records[0].Name}`);
            console.log(`   Org ID: ${result.records[0].Id}`);
        } else {
            console.log('⚠️ Query returned no results');
        }
        
        // Test object access
        console.log('');
        console.log('🔍 Testing object access...');
        
        const testObjects = [
            'Lead',
            'Contact', 
            'Docebo_Users__c',
            'Docebo_Course__c',
            'Docebo_Course_Enrollment__c'
        ];
        
        for (const objectName of testObjects) {
            try {
                const objectResult = await conn.sobject(objectName).describe();
                console.log(`   ✅ ${objectName}: ${objectResult.fields.length} fields`);
            } catch (objError) {
                console.log(`   ❌ ${objectName}: ${objError.message}`);
            }
        }
        
        console.log('');
        console.log('🎉 PRODUCTION SALESFORCE CONNECTION TEST PASSED!');
        console.log('🚀 You can now run the historical data sync.');
        
    } catch (error) {
        console.error('❌ Connection test failed:');
        console.error(`   Error: ${error.message}`);
        console.error('');
        console.error('🔧 Troubleshooting:');
        console.error('   1. Check your .env file has production credentials');
        console.error('   2. Verify your username/password/security token');
        console.error('   3. Ensure your Connected App is configured correctly');
        console.error('   4. Check if your IP is whitelisted in Salesforce');
        console.error('');
        console.error('💡 Common issues:');
        console.error('   - Security token concatenated with password incorrectly');
        console.error('   - Using sandbox URLs instead of production URLs');
        console.error('   - Connected App not approved or configured');
        console.error('   - User permissions insufficient');
        
        process.exit(1);
    }
}

// Run the test
testProductionConnection().catch(console.error);
