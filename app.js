// Initialize logger first so all logs are captured
require('./utils/logger');

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const globalService = require("./platform/global");
const http = require('http');
const path = require('path');

const PORT = process.env.PORT || 5000;
const NODE_ENV = process.env.NODE_ENV || 'development';

const app = express();
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({extended: true}));

// Serve daily log files from the logs directory (no auth)
app.use('/logs', express.static(path.join(__dirname, 'logs')));

const doceboRoute = require("./platform/docebo/router")
// const salesforceRoute = require("./platform/salesforce/router")

app.use("/webhook/docebo", doceboRoute);

// Health check endpoint
app.get('/health', (_, res) => {
    res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        environment: NODE_ENV
    });
});



// Start HTTP server (SSL handled by Apache/Nginx vhost)
function startServers() {
    console.log(`Starting application in ${NODE_ENV} mode...`);
    console.log("Logs are being saved to daily log files in the logs directory");
    console.log("SSL is handled by your web server (Apache/Nginx vhost)");

    // Start HTTP server - web server will proxy HTTPS requests to this
    const httpServer = http.createServer(app);
    httpServer.listen(PORT, () => {
        console.log(`✅ HTTP Server running on port ${PORT}`);
        console.log(`   Health check: http://localhost:${PORT}/health`);
        console.log(`   External access via web server: https://communityreport.org`);
        console.log(`   Webhook endpoints available at:`);
        console.log(`   - https://communityreport.org/webhook/docebo/user/manage`);
        console.log(`   - https://communityreport.org/webhook/docebo/course/manage`);
        console.log(`   - https://communityreport.org/webhook/docebo/session/manage`);
        console.log(`   - https://communityreport.org/webhook/docebo/lp/manage`);
        console.log(`   - https://communityreport.org/webhook/docebo/course/completed`);
    });

    httpServer.on('error', (error) => {
        if (error.code === 'EACCES') {
            console.error(`❌ Permission denied to bind to port ${PORT}`);
        } else if (error.code === 'EADDRINUSE') {
            console.error(`❌ Port ${PORT} is already in use`);
            console.error('   Try using a different port or stop the conflicting process');
            console.error('   Check what\'s using the port: sudo lsof -i :${PORT}');
        } else {
            console.error('❌ HTTP Server error:', error.message);
        }
    });

    // Initialize cron jobs
    globalService.initCronJob();
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
    console.log('Received SIGTERM, shutting down gracefully...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('Received SIGINT, shutting down gracefully...');
    process.exit(0);
});

// Start the application
startServers();
