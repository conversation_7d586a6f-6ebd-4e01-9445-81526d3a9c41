#!/usr/bin/env node

require('dotenv').config();
const getConnection = require('../platform/salesforce/common/getConnection');

async function main() {
  const conn = await getConnection();
  if (!conn || !conn.accessToken) {
    console.error('Invalid Salesforce connection');
    process.exit(1);
  }

  // Docebo_Users__c payload (only fields that exist on DU)
  const duData = {
    User_Unique_Id__c: 198679,
    User_Level__c: '6',
    Deactivated__c: true,
    User_Creation_Date__c: '2025-08-26T19:47:41.000Z',
    User_Expiration_Date__c: '2025-08-26T19:47:41.000Z',
    User_Last_Access_Date__c: null,
    User_Suspension_Date__c: null,
    Email_Validation_Status__c: true,
    First_Name__c: 'Kelsey',
    Last_Name__c: 'Merrill',
    Full_Name__c: '<PERSON>',
    Email__c: '<EMAIL>',
    Organization_Name__c: '-',
    Organization_URL__c: 'eqew',
    Organization_Headquarters__c: '',
    Branches_Codes__c: 0,
    Job_Title__c: 'ewqsadwq',
    Employment_Type__c: '',
    Role_Type__c: 'Other',
    Employment_Begin_Date__c: null,
    Direct_Manager__c: null,
    Backbone_Partner__c: false,
    Back_Partner_Type__c: '',
    Gender_Identity__c: 'Prefer not to respond',
    Race_Identity__c: 'Other',
    Initiative__c: 'FUSE',
    National_Regional_or_Local__c: '',
    Languages__c: 'english',
    Time_Zone__c: 'Europe/Budapest',
    Network_Partnership_Association__c: '',
    City__c: 'dsadqweqw',
    State__c: 'Arizona'
  };

  // Lead payload (as requested)
  const leadData = {
    LastName: 'Merrill',
    FirstName: 'Kelsey',
    Email: '<EMAIL>',
    Company: '-',
    Title: 'ewqsadwq',
    Website: 'eqew',
    Organization_URL__c: 'eqew',
    Status: 'Open - Not Contacted',
    Created_by_Docebo_API__c: true,
    GenderIdentity: 'Prefer not to respond',
    Role_Type__c: 'Other',
    NumberOfEmployees: '55',
    Race__c: 'Other',
    Description: 'Docebo user - Level: 6, Branch: N/A',
    Fax: '',
    Salutation: '',
    Phone: '',
    Languages__c: 'english',
    mailingcity__c: 'dsadqweqw',
    mailingcountry__c: '',
    mailingpostalcode__c: '',
    mailingstate__c: 'Arizona',
    mailingstreet__c: '',
    position_role__c: 'Other',
    LeadSource: 'Docebo Platform',
    accountid__c: '',
    AnnualRevenue: 0,
    Industry: 'Not For Profit',
    Initiative__c: 'FUSE',
    NumberOfEmployees: 0,
    Organization_employer__c: '',
    Rating: 'Warm',
    Time_Zone__c: 'Europe/Budapest',
    Type__c: 'Backbone Staff',
    Network_Partnership_Association__c: '',
    Active_Portal_User__c: true,
    FTE__c: 'Full-Time',
    Gateway__c: 'Docebo API',
    Inactive_Contact__c: false,
    Legacy_Id__c: '39999',
    No_Longer_Leadership__c: false,
    No_Longer_Staff__c: false,
    Number_of_Years_in_the_Partnership__c: 0,
    //OwnerId: '005O400000BxnnxIAB',
    Contact_Type__c: 'Other'
  };

  try {
    console.log('Creating Docebo_Users__c...');
    const duCreate = await conn.sobject('Docebo_Users__c').create(duData);
    if (!duCreate.success) {
      console.error('DU create failed:', duCreate.errors);
      process.exit(1);
    }
    const duId = duCreate.id;
    console.log('DU created:', duId);

    console.log('Creating Lead...');
    const leadCreate = await conn.sobject('Lead').create(leadData);
    if (!leadCreate.success) {
      console.error('Lead create failed:', leadCreate.errors);
      process.exit(1);
    }
    const leadId = leadCreate.id;
    console.log('Lead created:', leadId);

    console.log('Linking DU -> Lead...');
    const duLink = await conn.sobject('Docebo_Users__c').update({ Id: duId, Lead__c: leadId, Email__c: leadData.Email });
    if (!duLink.success) {
      console.error('DU->Lead link failed:', duLink.errors);
    } else {
      console.log('Linked DU -> Lead');
    }

    // Optional back-reference if the field exists
    try {
      console.log('Attempting Lead -> DU back-reference...');
      const leadDescribe = await conn.sobject('Lead').describe();
      const duField = leadDescribe.fields.find(f => f.name === 'Docebo_User__c');
      if (duField) {
        const backRef = await conn.sobject('Lead').update({ Id: leadId, Docebo_User__c: duId });
        if (backRef.success) {
          console.log('Linked Lead -> DU');
        } else {
          console.log('Lead->DU back-ref failed:', backRef.errors);
        }
      } else {
        console.log('Lead.Docebo_User__c not present; skipping back-reference');
      }
    } catch (e) {
      console.log('Back-reference attempt error:', e && (e.message || e.toString()));
    }

    console.log('Done.');
  } catch (e) {
    console.error('Unexpected error:', e && (e.message || e.toString()));
    process.exit(1);
  }
}

main();

