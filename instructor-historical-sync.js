#!/usr/bin/env node

/**
 * Instructor Historical Sync
 *
 * This script fetches all instructors from Docebo and creates Instructor__c records in Salesforce
 */

// Toggle file logging here (default: enabled). Set to false to disable.
const ENABLE_FILE_LOG = true;
if (ENABLE_FILE_LOG && !process.env.INSTRUCTOR_SYNC_FILE_LOG && !process.env.HISTORICAL_FILE_LOG) {
  process.env.INSTRUCTOR_SYNC_FILE_LOG = '1';
}

require('dotenv').config();

// Optional file logging for sync runs
const fs = require('fs');
const path = require('path');
(function setupFileLogger() {
  try {
    if (process.env.INSTRUCTOR_SYNC_FILE_LOG === '1' || process.env.HISTORICAL_FILE_LOG === '1') {
      const logsDir = path.join(process.cwd(), 'logs');
      if (!fs.existsSync(logsDir)) fs.mkdirSync(logsDir, { recursive: true });
      const ts = new Date().toISOString().replace(/[:.]/g, '-');
      const logPath = path.join(logsDir, `instructor-historical-sync-${ts}.log`);
      const stream = fs.createWriteStream(logPath, { flags: 'a' });

      const origLog = console.log.bind(console);
      const origWarn = console.warn.bind(console);
      const origError = console.error.bind(console);

      const safe = (v) => {
        if (v instanceof Error) return `${v.name}: ${v.message}`;
        if (typeof v === 'string') return v;
        try { return JSON.stringify(v); } catch { return String(v); }
      };
      const stamp = (lvl, args) => `[${new Date().toISOString()}] [${lvl}] ${args.map(safe).join(' ')}\n`;

      console.log = (...args) => { try { stream.write(stamp('INFO', args)); } catch {} origLog(...args); };
      console.warn = (...args) => { try { stream.write(stamp('WARN', args)); } catch {} origWarn(...args); };
      console.error = (...args) => { try { stream.write(stamp('ERROR', args)); } catch {} origError(...args); };

      origLog(`📝 Instructor historical sync file logging enabled: ${logPath}`);
    }
  } catch (e) {
    console.error('File logging setup failed:', e);
  }
})();

const doceboService = require("./platform/docebo/services");
const getConnection = require('./platform/salesforce/common/getConnection');

// Instructor template for Salesforce
const instructorTemplate = {
  Name: "",  // UserID field
  First_Name__c: "",
  Last_Name__c: "",
  Email__c: "",
  Idst__c: "",
  Avatar__c: "",
  urlAvatar__c: "",
  User_External_Id__c: "" // Docebo User ID for reference
};

async function createInstructorRecord(conn, instructorData) {
    try {
        // Extract instructor information
        const userId = instructorData.id || instructorData.user_id;
        const firstName = instructorData.firstname || instructorData.first_name || "";
        const lastName = instructorData.lastname || instructorData.last_name || "";
        const email = instructorData.email || "";
        const avatar = instructorData.avatar || "";
        
        // Check if instructor already exists
        const existingInstructor = await conn.sobject("Instructor__c")
            .findOne({ Name: userId.toString() });
        
        if (existingInstructor) {
            console.log(`   ✅ Instructor ${userId} already exists (${firstName} ${lastName})`);
            return existingInstructor.Id;
        }
        
        // Create new instructor record
        const instructorRecord = { ...instructorTemplate };
        instructorRecord.Name = userId.toString();
        instructorRecord.First_Name__c = firstName;
        instructorRecord.Last_Name__c = lastName;
        instructorRecord.Email__c = email;
        instructorRecord.Avatar__c = avatar;
        instructorRecord.urlAvatar__c = avatar; // Same as avatar for now
        instructorRecord.User_External_Id__c = userId.toString();
        
        const result = await conn.sobject("Instructor__c").create(instructorRecord);
        
        if (result.success) {
            console.log(`   ✅ Created instructor ${userId}: ${firstName} ${lastName} (${email})`);
            return result.id;
        } else {
            console.error(`   ❌ Failed to create instructor ${userId}:`, result.errors);
            return null;
        }
        
    } catch (error) {
        console.error(`   ❌ Error creating instructor record:`, error.message);
        return null;
    }
}

async function fetchAllInstructors() {
    try {
        console.log('📥 Fetching all instructors from Docebo...');
        
        // We need to get instructors from sessions since there's no direct instructors API
        // First get all courses
        const courseListedInfo = await doceboService.getTotalCourseListedInfo();
        
        if (!courseListedInfo || courseListedInfo.length === 0) {
            console.log('⚠️ No courses found in Docebo');
            return [];
        }
        
        console.log(`✅ Found ${courseListedInfo.length} courses, scanning for instructors...`);
        
        const instructorMap = new Map(); // Use Map to avoid duplicates
        let coursesProcessed = 0;
        let sessionsProcessed = 0;
        
        // Process each course to find instructors
        for (const course of courseListedInfo) {
            coursesProcessed++;
            console.log(`📚 Scanning course ${coursesProcessed}/${courseListedInfo.length}: ${course.name} (ID: ${course.id})`);
            
            try {
                // Get sessions for this course
                const sessionListedInfo = await doceboService.getSessionListedInfo(course.id);
                
                if (!sessionListedInfo || sessionListedInfo.length === 0) {
                    continue; // No sessions, skip
                }
                
                // Process each session to find instructors
                for (const session of sessionListedInfo) {
                    sessionsProcessed++;
                    
                    try {
                        // Get detailed session info
                        const sessionInfo = await doceboService.getCourseSessionInfo(session.id);
                        
                        if (sessionInfo.status === 200 && sessionInfo.data && sessionInfo.data.instructors) {
                            // Add instructors to our map
                            for (const instructor of sessionInfo.data.instructors) {
                                const instructorId = instructor.user_id || instructor.id;
                                if (instructorId && !instructorMap.has(instructorId)) {
                                    instructorMap.set(instructorId, {
                                        id: instructorId,
                                        firstname: instructor.firstname || "",
                                        lastname: instructor.lastname || "",
                                        email: instructor.email || "",
                                        avatar: instructor.avatar || "",
                                        courseId: course.id,
                                        courseName: course.name,
                                        sessionId: session.id
                                    });
                                    console.log(`   👤 Found instructor: ${instructor.firstname} ${instructor.lastname} (ID: ${instructorId})`);
                                }
                            }
                        }
                        
                    } catch (sessionError) {
                        console.log(`   ⚠️ Error getting session ${session.id}:`, sessionError.message);
                    }
                }
                
            } catch (courseError) {
                console.log(`   ⚠️ Error processing course ${course.id}:`, courseError.message);
            }
        }
        
        const uniqueInstructors = Array.from(instructorMap.values());
        console.log(`\n✅ Scan completed:`);
        console.log(`   📚 Courses processed: ${coursesProcessed}`);
        console.log(`   📅 Sessions processed: ${sessionsProcessed}`);
        console.log(`   👥 Unique instructors found: ${uniqueInstructors.length}`);
        
        return uniqueInstructors;
        
    } catch (error) {
        console.error('❌ Error fetching instructors:', error.message);
        return [];
    }
}

async function instructorHistoricalSync() {
    try {
        console.log('🚀 STARTING INSTRUCTOR HISTORICAL SYNC');
        console.log('=' .repeat(60));
        console.log('This process will:');
        console.log('1. Scan all courses and sessions in Docebo');
        console.log('2. Extract all unique instructors');
        console.log('3. Create Instructor__c records in Salesforce');
        console.log('4. Prevent duplicates by checking existing records');
        console.log('');

        const startTime = Date.now();

        // Step 1: Test Salesforce connection
        console.log('🔐 Testing Salesforce connection...');
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return false;
        }
        console.log('✅ Salesforce connection established');

        // Step 2: Fetch all instructors from Docebo
        const instructors = await fetchAllInstructors();
        
        if (instructors.length === 0) {
            console.log('⚠️ No instructors found in Docebo');
            return false;
        }

        // Step 3: Create instructor records in Salesforce
        console.log(`\n💾 Creating ${instructors.length} instructor records in Salesforce...`);
        
        let successCount = 0;
        let existingCount = 0;
        let errorCount = 0;

        for (let i = 0; i < instructors.length; i++) {
            const instructor = instructors[i];
            console.log(`\n👤 Processing instructor ${i + 1}/${instructors.length}: ${instructor.firstname} ${instructor.lastname} (ID: ${instructor.id})`);
            
            const instructorId = await createInstructorRecord(conn, instructor);
            
            if (instructorId) {
                if (instructorId === 'existing') {
                    existingCount++;
                } else {
                    successCount++;
                }
            } else {
                errorCount++;
            }
        }

        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);

        console.log('\n' + '='.repeat(60));
        console.log('🎉 INSTRUCTOR HISTORICAL SYNC COMPLETED!');
        console.log(`⏱️  Total time: ${duration} seconds`);
        console.log(`✅ Created: ${successCount} instructors`);
        console.log(`🔄 Already existed: ${existingCount} instructors`);
        console.log(`❌ Errors: ${errorCount} instructors`);
        console.log(`📊 Total processed: ${successCount + existingCount + errorCount} instructors`);
        console.log('');
        
        if (successCount > 0 || existingCount > 0) {
            console.log('✅ Instructor sync completed successfully!');
            console.log('   • All unique instructors identified from sessions');
            console.log('   • Instructor__c records created/verified in Salesforce');
            console.log('   • Ready for instructor-session associations');
            console.log('');
            console.log('🎯 Next step: Run instructor associations with sessions');
            console.log('   node other/process-all-instructor-associations.js');
        }

        return successCount > 0 || existingCount > 0;

    } catch (error) {
        console.error('❌ Instructor historical sync failed:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Run the sync
instructorHistoricalSync().catch(console.error);
