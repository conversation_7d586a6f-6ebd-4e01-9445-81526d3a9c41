#!/usr/bin/env node

/**
 * Check Created Courses
 * 
 * This script queries Salesforce to show the courses we just created
 */

require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkCreatedCourses() {
    try {
        console.log('🔍 CHECKING CREATED COURSES IN SALESFORCE');
        console.log('=' .repeat(50));
        
        // Get Salesforce connection
        console.log('🔐 Connecting to Salesforce...');
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return false;
        }
        console.log('✅ Salesforce connection established');
        
        // Query the courses we just created (External IDs: 3, 4, 5, 6, 7)
        console.log('\n📋 Querying recently created courses...');
        
        const courseQuery = `
            SELECT Id, Name, Course_Name__c, Course_External_Id__c, Course_Type__c, 
                   Course_Status__c, Course_Category__c, Course_Creation_Date__c,
                   Enrollment_Date__c, Session_Time_min__c, CreatedDate, LastModifiedDate
            FROM Docebo_Course__c 
            WHERE Course_External_Id__c IN (3, 4, 5, 6, 7)
            ORDER BY Course_External_Id__c
        `;
        
        const courses = await conn.query(courseQuery);
        
        if (!courses.records || courses.records.length === 0) {
            console.log('⚠️ No courses found with External IDs 3, 4, 5, 6, 7');
            console.log('   They may not have been created successfully or may have different IDs');
            return false;
        }
        
        console.log(`✅ Found ${courses.records.length} courses:`);
        console.log('');
        
        // Display each course
        courses.records.forEach((course, index) => {
            console.log(`📚 Course ${index + 1}:`);
            console.log(`   Salesforce ID: ${course.Id}`);
            console.log(`   Name: ${course.Course_Name__c || course.Name || 'N/A'}`);
            console.log(`   External ID: ${course.Course_External_Id__c}`);
            console.log(`   Type: ${course.Course_Type__c || 'N/A'}`);
            console.log(`   Status: ${course.Course_Status__c || 'N/A'}`);
            console.log(`   Category: ${course.Course_Category__c || 'N/A'}`);
            console.log(`   Session Time: ${course.Session_Time_min__c || 0} minutes`);
            console.log(`   Created: ${course.CreatedDate ? new Date(course.CreatedDate).toLocaleString() : 'N/A'}`);
            console.log(`   Modified: ${course.LastModifiedDate ? new Date(course.LastModifiedDate).toLocaleString() : 'N/A'}`);
            console.log('');
        });
        
        // Also check total course count
        console.log('📊 TOTAL COURSE STATISTICS:');
        const totalQuery = 'SELECT COUNT() FROM Docebo_Course__c';
        const totalResult = await conn.query(totalQuery);
        console.log(`   Total courses in Salesforce: ${totalResult.totalSize}`);
        
        // Check recent courses (created in last hour)
        const recentQuery = `
            SELECT COUNT() FROM Docebo_Course__c 
            WHERE CreatedDate >= ${new Date(Date.now() - 60*60*1000).toISOString()}
        `;
        const recentResult = await conn.query(recentQuery);
        console.log(`   Courses created in last hour: ${recentResult.totalSize}`);
        
        return true;
        
    } catch (error) {
        console.error('❌ Error checking courses:', error.message);
        if (error.stack) {
            console.error('Stack trace:', error.stack);
        }
        return false;
    }
}

// Run the check
checkCreatedCourses().catch(console.error);
