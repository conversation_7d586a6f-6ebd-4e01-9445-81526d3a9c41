module.exports = {
  apps: [{
    name: 'docebo-salesforce',
    script: 'app.js',
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      DISABLE_FILE_LOGS: '0',  // Force enable file logging under PM2

      // Webhook URLs
      DOCEBO_WEBHOOK_URL: 'https://communityreport.org',
      SALESFORCE_WEBHOOK_URL: 'https://communityreport.org',
      HUBSPOT_WEBHOOK_URL: 'https://communityreport.org',

      // Docebo API credentials
      DOCEBO_API_BASE_URL: 'https://strivetogether.docebosaas.com',
      DOCEBO_API_SUB_DOMAIN: 'https://strivetogether.docebosaas.com/oauth2/token',
      DOCEBO_API_CLIENT_NAME: 'sf-hs-dev',
      DOCEBO_API_CLIENT_SECRET: '****************************************************************',
      DOCEBO_API_GRANT_TYPE: 'password',
      DOCEBO_API_SCOPE: 'api',
      DOCEBO_API_USER_NAME: '<EMAIL>',
      DOCEBO_API_PASSWORD: 'Connect2024!',

      // Salesforce API credentials
      SF_TOKEN_URL: 'https://strivetogether.my.salesforce.com/services/oauth2/token',
      SF_API_INSTANCE: 'https://strivetogether.my.salesforce.com',
      SF_API_CLIENT_ID: '3MVG9IHf89I1t8hrNr9NEQVtOJ0PfCWPFEPgOSwmbuu2iW41MdJ1bZ0JH2041kbUbeFo1SLqCMESQCFZsGjMw',
      SF_API_CLIENT_SECRET: '****************************************************************',
      SF_API_GRANT_TYPE: 'client_credentials',
      SF_API_USER_NAME: '<EMAIL>',
      SF_API_PASSWORD: '6aRJmBSUcRPGv8D3cQp4tdrskpRGvrcQyonKN6C',
      SF_API_ORG_PASS: '6aRJmBSUcRPGv8D',
      SF_API_ACCESS_TOKEN: '00DO4000002rak9!AQEAQBGhe8hPvFj82bLdGu.ojtmvvHGsQBF8ZvJwEM8nDaanoUTPsR8lYZkSEXrmCbHX4DfILjPf8oPd_HN4lLWD1bcrFiIA',
      SF_API_ID: 'https://login.salesforce.com/id/00DO4000002rak9MAA/005O400000FAZCRIA5',

      // HubSpot API credentials
      HS_ACCESS_TOKEN: '********************************************',
      HS_ACCESS_SECRET: '17e470f9-39e1-45f9-a81a-d59bfbe4b745'
    },
    // PM2 specific options
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    // Log configuration
    log_file: './logs/pm2-combined.log',
    out_file: './logs/pm2-out.log',
    error_file: './logs/pm2-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z'
  }]
};
