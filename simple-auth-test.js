#!/usr/bin/env node

/**
 * Simple Salesforce Authentication Test
 * 
 * This is the most basic test possible - just try to get an access token
 */

require('dotenv').config();
const axios = require('axios');

async function simpleAuthTest() {
    console.log('🔐 SIMPLE SALESFORCE AUTH TEST');
    console.log('=' .repeat(40));
    
    // Get values from .env
    const tokenUrl = process.env.SF_TOKEN_URL;
    const clientId = process.env.SF_API_CLIENT_ID;
    const clientSecret = process.env.SF_API_CLIENT_SECRET;
    const username = process.env.SF_API_USER_NAME;
    const password = process.env.SF_API_PASSWORD;
    
    console.log('📋 Using credentials:');
    console.log(`   URL: ${tokenUrl}`);
    console.log(`   Username: ${username}`);
    console.log(`   Client ID: ${clientId ? clientId.substring(0, 15) + '...' : 'MISSING'}`);
    console.log(`   Password length: ${password ? password.length : 0} chars`);
    console.log('');
    
    // Check if all required fields are present
    if (!tokenUrl || !clientId || !clientSecret || !username || !password) {
        console.log('❌ Missing required credentials in .env file');
        return;
    }
    
    console.log('🚀 Attempting authentication...');
    
    try {
        // Build request parameters based on grant type
        const grantType = process.env.SF_API_GRANT_TYPE || 'password';
        const params = {
            grant_type: grantType,
            client_id: clientId,
            client_secret: clientSecret
        };

        // Only add username/password for password grant type
        if (grantType === 'password') {
            params.username = username;
            params.password = password;
        }

        console.log(`   Grant Type: ${grantType}`);
        console.log('');

        // Make the authentication request
        const response = await axios.post(
            tokenUrl,
            new URLSearchParams(params),
            {
                headers: { 
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Accept': 'application/json'
                },
                timeout: 30000
            }
        );
        
        // Success!
        console.log('✅ SUCCESS! Authentication worked!');
        console.log('');
        console.log('📊 Response details:');
        console.log(`   Access Token: ${response.data.access_token.substring(0, 20)}...`);
        console.log(`   Instance URL: ${response.data.instance_url}`);
        console.log(`   Token Type: ${response.data.token_type}`);
        console.log(`   Issued At: ${new Date(parseInt(response.data.issued_at)).toISOString()}`);
        console.log('');
        console.log('🎉 Your credentials are working correctly!');
        console.log('🚀 You can now run the historical data sync.');
        
    } catch (error) {
        console.log('❌ FAILED! Authentication error:');
        console.log('');
        
        if (error.response) {
            // Server responded with error
            console.log(`   HTTP Status: ${error.response.status}`);
            console.log(`   Error Code: ${error.response.data.error}`);
            console.log(`   Description: ${error.response.data.error_description}`);
            
            // Specific guidance based on error
            if (error.response.data.error === 'invalid_grant') {
                console.log('');
                console.log('🔧 INVALID_GRANT means one of these issues:');
                console.log('   1. ❌ Wrong username for this org');
                console.log('   2. ❌ Wrong password');
                console.log('   3. ❌ Wrong or expired security token');
                console.log('   4. ❌ User account locked or inactive');
                console.log('   5. ❌ IP address not trusted');
                console.log('   6. ❌ User missing API permissions');
                console.log('');
                console.log('💡 Try these fixes:');
                console.log('   • Log into Salesforce web with same username/password');
                console.log('   • Reset security token: Profile → Settings → Reset Security Token');
                console.log('   • Check Network Access: Setup → Network Access');
                console.log('   • Assign API Access permission set to user');
            }
            
            if (error.response.data.error === 'invalid_client_id') {
                console.log('');
                console.log('🔧 INVALID_CLIENT_ID means:');
                console.log('   • Wrong Consumer Key from Connected App');
                console.log('   • Connected App not found in this org');
            }
            
            if (error.response.data.error === 'invalid_client') {
                console.log('');
                console.log('🔧 INVALID_CLIENT means:');
                console.log('   • Wrong Consumer Secret from Connected App');
                console.log('   • Connected App configuration issue');
            }
            
        } else if (error.request) {
            console.log('   Network error - no response received');
            console.log(`   ${error.message}`);
        } else {
            console.log(`   Unexpected error: ${error.message}`);
        }
    }
}

// Run the test
simpleAuthTest().catch(console.error);
