#!/bin/bash

# SSL Setup Script for communityreport.org
# This script sets up SSL certificates and configures the application

set -e  # Exit on any error

echo "🔒 Setting up SSL for communityreport.org..."

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "❌ This script should not be run as root for security reasons"
   echo "   Please run as a regular user with sudo privileges"
   exit 1
fi

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Install required packages
echo "📦 Installing required packages..."
sudo apt update
sudo apt install -y certbot authbind

# Stop any services that might be using port 80 (needed for certbot)
echo "🛑 Stopping services that might conflict with certbot..."
sudo systemctl stop nginx apache2 2>/dev/null || true

# Get SSL certificate for communityreport.org
echo "🔐 Obtaining SSL certificate for communityreport.org..."
if [ ! -d "/etc/letsencrypt/live/communityreport.org" ]; then
    sudo certbot certonly --standalone -d communityreport.org --agree-tos --non-interactive --email <EMAIL>
else
    echo "   SSL certificate already exists for communityreport.org"
fi

# Create SSL directories
echo "📁 Creating SSL directories..."
sudo mkdir -p /etc/ssl/private /etc/ssl/certs

# Copy certificates to expected locations
echo "📋 Copying certificates..."
sudo cp /etc/letsencrypt/live/communityreport.org/privkey.pem /etc/ssl/private/server.key
sudo cp /etc/letsencrypt/live/communityreport.org/fullchain.pem /etc/ssl/certs/server.crt

# Set proper permissions
echo "🔐 Setting certificate permissions..."
sudo chmod 600 /etc/ssl/private/server.key
sudo chmod 644 /etc/ssl/certs/server.crt
sudo chown root:root /etc/ssl/private/server.key /etc/ssl/certs/server.crt

# Set up authbind for port 443 (allows non-root user to bind to port 443)
echo "🔧 Setting up authbind for port 443..."
sudo touch /etc/authbind/byport/443
sudo chmod 500 /etc/authbind/byport/443
sudo chown $USER /etc/authbind/byport/443

# Set up authbind for port 80 (optional, for HTTP redirect)
sudo touch /etc/authbind/byport/80
sudo chmod 500 /etc/authbind/byport/80
sudo chown $USER /etc/authbind/byport/80

# Create certificate renewal script
echo "🔄 Setting up certificate auto-renewal..."
sudo tee /etc/cron.d/certbot-renewal > /dev/null <<EOF
# Renew certificates monthly and restart the application
0 2 1 * * root /usr/bin/certbot renew --quiet --deploy-hook "/bin/bash -c 'cp /etc/letsencrypt/live/communityreport.org/privkey.pem /etc/ssl/private/server.key && cp /etc/letsencrypt/live/communityreport.org/fullchain.pem /etc/ssl/certs/server.crt && chmod 600 /etc/ssl/private/server.key && chmod 644 /etc/ssl/certs/server.crt && systemctl restart docebo-salesforce 2>/dev/null || pkill -f \"node app.js\" || true'"
EOF

# Create systemd service file for the application
echo "⚙️ Creating systemd service..."
sudo tee /etc/systemd/system/docebo-salesforce.service > /dev/null <<EOF
[Unit]
Description=Docebo Salesforce Integration
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/var/www/communityreport.org
ExecStart=/usr/bin/authbind --deep /usr/bin/node app.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and enable the service
sudo systemctl daemon-reload
sudo systemctl enable docebo-salesforce

echo "✅ SSL setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Make sure your application is in /var/www/communityreport.org/"
echo "2. Install dependencies: cd /var/www/communityreport.org && npm install"
echo "3. Configure your .env file with production settings"
echo "4. Start the service: sudo systemctl start docebo-salesforce"
echo "5. Check status: sudo systemctl status docebo-salesforce"
echo ""
echo "🔍 Testing commands:"
echo "   curl https://communityreport.org/health"
echo "   curl -k https://localhost:443/health"
echo ""
echo "📊 Monitoring commands:"
echo "   sudo systemctl status docebo-salesforce"
echo "   sudo journalctl -u docebo-salesforce -f"
echo ""
echo "🔒 Your SSL certificate will auto-renew monthly"
