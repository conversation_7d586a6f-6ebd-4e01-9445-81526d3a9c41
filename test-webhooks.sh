#!/bin/bash

# Test script for Docebo webhook endpoints
# All webhook endpoints require POST requests, not GET

echo "🧪 Testing Docebo Webhook Endpoints..."
echo "========================================="

# Test 1: Health Check (GET request - should work)
echo "1. Testing Health Check (GET):"
curl -s https://communityreport.org/health
echo -e "\n"

# Test 2: User Management Webhook (POST request)
echo "2. Testing User Management Webhook (POST):"
curl -X POST https://communityreport.org/webhook/docebo/user/manage \
  -H "Content-Type: application/json" \
  -d '{
    "message_id": "test-user-123",
    "event": "user.created",
    "payload": {
      "user_id": "12345",
      "username": "testuser",
      "email": "<EMAIL>"
    }
  }'
echo -e "\n"

# Test 3: Course Management Webhook (POST request)
echo "3. Testing Course Management Webhook (POST):"
curl -X POST https://communityreport.org/webhook/docebo/course/manage \
  -H "Content-Type: application/json" \
  -d '{
    "message_id": "test-course-123",
    "event": "course.created",
    "payload": {
      "course_id": "12345",
      "course_name": "Test Course"
    }
  }'
echo -e "\n"

# Test 4: Session Management Webhook (POST request)
echo "4. Testing Session Management Webhook (POST):"
curl -X POST https://communityreport.org/webhook/docebo/session/manage \
  -H "Content-Type: application/json" \
  -d '{
    "message_id": "test-session-123",
    "event": "ilt.session.created",
    "payload": {
      "session_id": "12345",
      "course_id": "67890"
    }
  }'
echo -e "\n"

# Test 5: Learning Plan Management Webhook (POST request)
echo "5. Testing Learning Plan Management Webhook (POST):"
curl -X POST https://communityreport.org/webhook/docebo/lp/manage \
  -H "Content-Type: application/json" \
  -d '{
    "message_id": "test-lp-123",
    "event": "learningplan.enrollment.created",
    "payload": {
      "user_id": "12345",
      "learningplan_id": "67890"
    }
  }'
echo -e "\n"

# Test 6: Course Completion Webhook (POST request)
echo "6. Testing Course Completion Webhook (POST):"
curl -X POST https://communityreport.org/webhook/docebo/course/completed \
  -H "Content-Type: application/json" \
  -d '{
    "message_id": "test-completion-123",
    "event": "course.completed",
    "payload": {
      "user_id": "12345",
      "course_id": "67890"
    }
  }'
echo -e "\n"

echo "========================================="
echo "✅ All webhook tests completed!"
echo ""
echo "📋 Expected responses:"
echo "- Health check: JSON with status 'healthy'"
echo "- All webhooks: JSON with status 'success' and processing message"
echo ""
echo "🔍 If any webhook returns an error, check your application logs:"
echo "   tail -f /var/www/communityreport.org/logs/app.log"
