#!/usr/bin/env node
require('dotenv').config();

(async () => {
  try {
    const [userId, emailArg] = process.argv.slice(2);
    if (!userId || !emailArg) {
      console.error('Usage: node scripts/create-lead-direct.js <docebo_user_id> <email>');
      process.exit(2);
    }
    const email = String(emailArg).trim().toLowerCase();

    const getConnection = require('../platform/salesforce/common/getConnection');
    const conn = await getConnection();
    if (!conn || !conn.accessToken) {
      console.error('❌ Could not obtain Salesforce connection.');
      process.exit(2);
    }

    // 1) If a Lead already exists for this email, return it
    const existingLead = await conn.query(`SELECT Id, Email, LastName, Company FROM Lead WHERE Email='${email}' LIMIT 1`);
    if (existingLead.records && existingLead.records.length > 0) {
      console.log(JSON.stringify({ result: 'exists', type: 'Lead', id: existingLead.records[0].Id, email }, null, 2));
      process.exit(0);
    }

    // 2) Create a minimal Lead
    const leadData = {
      LastName: 'WebhookTest',
      FirstName: 'Docebo',
      Company: 'Webhook Test',
      Email: email,
      LeadSource: 'Docebo Platform',
      Created_by_Docebo_API__c: true
    };

    const createRes = await conn.sobject('Lead').create(leadData);
    if (!createRes.success) {
      console.error('❌ Lead creation failed:', createRes.errors);
      process.exit(1);
    }
    const leadId = createRes.id;
    console.log(JSON.stringify({ result: 'created', type: 'Lead', id: leadId, email }, null, 2));

    // 3) Link Docebo_Users__c to the lead by user unique id if present
    try {
      const duRes = await conn.query(`SELECT Id, Email__c FROM Docebo_Users__c WHERE User_Unique_Id__c = '${userId}' LIMIT 1`);
      if (duRes.records && duRes.records.length > 0) {
        const duId = duRes.records[0].Id;
        // Align email
        await conn.sobject('Docebo_Users__c').update({ Id: duId, Email__c: email });
        const link = await conn.sobject('Docebo_Users__c').update({ Id: duId, Lead__c: leadId, Email__c: email });
        if (link && link.success) {
          console.log(JSON.stringify({ link: 'DU->Lead', duId, leadId }, null, 2));
          try {
            // Back-reference if field exists
            const leadDescribe = await conn.sobject('Lead').describe();
            const duLookup = leadDescribe.fields.find(f => f.name === 'Docebo_User__c');
            if (duLookup) {
              const backRef = await conn.sobject('Lead').update({ Id: leadId, Docebo_User__c: duId });
              if (backRef.success) console.log(JSON.stringify({ backref: 'Lead->DU', leadId, duId }, null, 2));
            }
          } catch (e) {
            console.warn('⚠️ Back-reference update skipped:', e && (e.message || e.toString()));
          }
        } else {
          console.warn('⚠️ Failed to link DU to Lead:', link && link.errors);
        }
      } else {
        console.warn('ℹ️ Docebo_Users__c not found by User_Unique_Id__c; skipping link');
      }
    } catch (e) {
      console.warn('⚠️ Error linking DU to Lead:', e && (e.message || e.toString()));
    }

    process.exit(0);
  } catch (e) {
    console.error('💥 Failure:', e && (e.message || e.toString()));
    process.exit(1);
  }
})();

