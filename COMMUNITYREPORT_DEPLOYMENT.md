# 🚀 Deployment Guide for communityreport.org

## 📋 Overview

This guide provides specific deployment instructions for your Docebo-Salesforce integration on the `communityreport.org` domain.

## 🎯 Your Webhook URLs

Once deployed, your webhook endpoints will be available at:

### **Docebo Webhook Endpoints:**
```
User Management:     https://communityreport.org/webhook/docebo/user/manage
Course Management:   https://communityreport.org/webhook/docebo/course/manage
Session Management:  https://communityreport.org/webhook/docebo/session/manage
Learning Plan:       https://communityreport.org/webhook/docebo/lp/manage
Course Completion:   https://communityreport.org/webhook/docebo/course/completed
```

### **Health Check:**
```
Health Status:       https://communityreport.org/health
```

---

## 🔧 Quick Deployment Steps

### Step 1: Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 and Certbot
sudo npm install -g pm2
sudo apt install -y certbot

# Create application user
sudo adduser --disabled-password --gecos "" docebo-app
sudo usermod -aG sudo docebo-app
```

### Step 2: Get SSL Certificate for communityreport.org
```bash
# Stop any services using port 80 (if running)
sudo systemctl stop apache2 nginx 2>/dev/null || true

# Get Let's Encrypt certificate
sudo certbot certonly --standalone -d communityreport.org

# Copy certificates to expected locations
sudo mkdir -p /etc/ssl/private /etc/ssl/certs
sudo cp /etc/letsencrypt/live/communityreport.org/privkey.pem /etc/ssl/private/server.key
sudo cp /etc/letsencrypt/live/communityreport.org/fullchain.pem /etc/ssl/certs/server.crt

# Set permissions
sudo chmod 600 /etc/ssl/private/server.key
sudo chmod 644 /etc/ssl/certs/server.crt
sudo chown root:root /etc/ssl/private/server.key /etc/ssl/certs/server.crt
```

### Step 3: Deploy Application
```bash
# Switch to application user
sudo su - docebo-app

# Clone your repository
cd /home/<USER>
git clone <your-repository-url> docebo-salesforce
cd docebo-salesforce

# Install dependencies
npm install --production

# Your .env file is already configured with communityreport.org URLs
# Just verify the configuration:
cat .env
```

### Step 4: Configure Firewall
```bash
# Enable firewall
sudo ufw enable

# Allow necessary ports
sudo ufw allow 22/tcp   # SSH
sudo ufw allow 80/tcp   # HTTP (for Let's Encrypt renewal)
sudo ufw allow 443/tcp  # HTTPS (for webhooks)

# Check status
sudo ufw status
```

### Step 5: Start Application
```bash
# Start with PM2 (as docebo-app user)
pm2 start app.js --name "docebo-salesforce"

# Configure PM2 to start on boot
pm2 startup
# Copy and run the command that PM2 outputs

# Save PM2 configuration
pm2 save
```

---

## 🔍 Testing Your Deployment

### Test Health Check
```bash
# Test HTTP (internal)
curl http://localhost:5000/health

# Test HTTPS (external)
curl https://communityreport.org/health

# Expected response:
# {"status":"healthy","timestamp":"2024-01-XX...","environment":"production"}
```

### Test Webhook Endpoints
```bash
# Test user management webhook
curl -X POST https://communityreport.org/webhook/docebo/user/manage \
  -H "Content-Type: application/json" \
  -d '{
    "message_id": "test-123",
    "event": "user.created",
    "payloads": [{
      "user_id": "12345",
      "username": "testuser",
      "email": "<EMAIL>"
    }]
  }'

# Expected response:
# {"status":"success","message":"Webhook received, processing in background"}
```

### Verify SSL Certificate
```bash
# Check certificate details
openssl s_client -connect communityreport.org:443 -servername communityreport.org

# Check certificate expiration
echo | openssl s_client -connect communityreport.org:443 2>/dev/null | openssl x509 -noout -dates
```

---

## 📊 Monitoring & Maintenance

### Check Application Status
```bash
# PM2 status
pm2 status

# View logs
pm2 logs docebo-salesforce

# Monitor resources
pm2 monit

# Restart if needed
pm2 restart docebo-salesforce
```

### SSL Certificate Auto-Renewal
```bash
# Test renewal
sudo certbot renew --dry-run

# Set up auto-renewal cron job
sudo crontab -e

# Add this line to renew certificates and restart app:
0 2 1 * * /usr/bin/certbot renew --quiet --deploy-hook "cp /etc/letsencrypt/live/communityreport.org/privkey.pem /etc/ssl/private/server.key && cp /etc/letsencrypt/live/communityreport.org/fullchain.pem /etc/ssl/certs/server.crt && chmod 600 /etc/ssl/private/server.key && chmod 644 /etc/ssl/certs/server.crt && su - docebo-app -c 'pm2 restart docebo-salesforce'"
```

---

## 🔧 Docebo Webhook Configuration

In your Docebo admin panel, configure these webhook URLs:

### **User Management Webhooks:**
- **URL**: `https://communityreport.org/webhook/docebo/user/manage`
- **Events**: User created, User updated, User deleted, User suspended, User reactivated

### **Course Management Webhooks:**
- **URL**: `https://communityreport.org/webhook/docebo/course/manage`
- **Events**: Course created, Course updated, Course deleted

### **Session Management Webhooks:**
- **URL**: `https://communityreport.org/webhook/docebo/session/manage`
- **Events**: Session created, Session updated, Session deleted

### **Learning Plan Webhooks:**
- **URL**: `https://communityreport.org/webhook/docebo/lp/manage`
- **Events**: Learning plan enrollment, Learning plan completion

### **Course Completion Webhooks:**
- **URL**: `https://communityreport.org/webhook/docebo/course/completed`
- **Events**: Course completed, Course progress updated

---

## 🚨 Troubleshooting

### Common Issues:

**1. Port 443 Permission Denied**
```bash
# Use authbind to allow non-root user to bind to port 443
sudo apt install authbind
sudo touch /etc/authbind/byport/443
sudo chmod 500 /etc/authbind/byport/443
sudo chown docebo-app /etc/authbind/byport/443

# Restart with authbind
su - docebo-app
pm2 delete docebo-salesforce
authbind --deep pm2 start app.js --name "docebo-salesforce"
```

**2. Domain Not Resolving**
```bash
# Check DNS
nslookup communityreport.org
dig communityreport.org

# Make sure your domain points to your server's IP
```

**3. SSL Certificate Issues**
```bash
# Check if certificates exist
ls -la /etc/ssl/private/server.key
ls -la /etc/ssl/certs/server.crt

# Regenerate if needed
sudo certbot certonly --standalone -d communityreport.org --force-renewal
```

**4. Application Not Starting**
```bash
# Check logs
pm2 logs docebo-salesforce

# Check if ports are available
sudo netstat -tulpn | grep :443
sudo netstat -tulpn | grep :5000

# Restart application
pm2 restart docebo-salesforce
```

---

## 📋 Post-Deployment Checklist

- [ ] SSL certificate installed and working
- [ ] Application starts on both HTTP (5000) and HTTPS (443)
- [ ] Health check endpoint responds: `https://communityreport.org/health`
- [ ] All 5 webhook endpoints respond with success messages
- [ ] PM2 configured to start on boot
- [ ] Firewall configured (ports 22, 80, 443 open)
- [ ] SSL auto-renewal configured
- [ ] Docebo webhooks configured with communityreport.org URLs
- [ ] Application logs are being written and rotated
- [ ] Monitoring/alerting set up (optional but recommended)

---

## 🎯 Next Steps

1. **Configure Docebo Webhooks**: Use the URLs listed above in your Docebo admin panel
2. **Test Integration**: Send test webhooks from Docebo to verify everything works
3. **Monitor Logs**: Watch PM2 logs for any errors during initial webhook processing
4. **Set Up Monitoring**: Consider adding uptime monitoring for your endpoints
5. **Backup Strategy**: Set up regular backups of your application and SSL certificates

Your Docebo-Salesforce integration is now ready for production at `https://communityreport.org/`!
