#!/usr/bin/env node

/**
 * Test Small Course Sync - 5 Courses Only
 *
 * This script tests the course sync process with just 5 courses to verify everything works
 */

// Toggle file logging here (default: enabled). Set to false to disable.
const ENABLE_FILE_LOG = true;
if (ENABLE_FILE_LOG && !process.env.SMALL_SYNC_FILE_LOG && !process.env.HISTORICAL_FILE_LOG) {
  process.env.SMALL_SYNC_FILE_LOG = '1';
}

require('dotenv').config();

// Optional file logging for test runs: set SMALL_SYNC_FILE_LOG=1 or HISTORICAL_FILE_LOG=1 to enable
const fs = require('fs');
const path = require('path');
(function setupFileLogger() {
  try {
    if (process.env.SMALL_SYNC_FILE_LOG === '1' || process.env.HISTORICAL_FILE_LOG === '1') {
      const logsDir = path.join(process.cwd(), 'logs');
      if (!fs.existsSync(logsDir)) fs.mkdirSync(logsDir, { recursive: true });
      const ts = new Date().toISOString().replace(/[:.]/g, '-');
      const logPath = path.join(logsDir, `small-course-sync-${ts}.log`);
      const stream = fs.createWriteStream(logPath, { flags: 'a' });

      const origLog = console.log.bind(console);
      const origWarn = console.warn.bind(console);
      const origError = console.error.bind(console);

      const safe = (v) => {
        if (v instanceof Error) return `${v.name}: ${v.message}`;
        if (typeof v === 'string') return v;
        try { return JSON.stringify(v); } catch { return String(v); }
      };
      const stamp = (lvl, args) => `[${new Date().toISOString()}] [${lvl}] ${args.map(safe).join(' ')}\n`;

      console.log = (...args) => { try { stream.write(stamp('INFO', args)); } catch {} origLog(...args); };
      console.warn = (...args) => { try { stream.write(stamp('WARN', args)); } catch {} origWarn(...args); };
      console.error = (...args) => { try { stream.write(stamp('ERROR', args)); } catch {} origError(...args); };

      origLog(`📝 Small-course-sync file logging enabled: ${logPath}`);
    }
  } catch (e) {
    console.error('File logging setup failed:', e);
  }
})();

const doceboService = require("./platform/docebo/services");
const { createNewCourse } = require("./platform/salesforce/courses/createCourse");
const getConnection = require('./platform/salesforce/common/getConnection');

async function testSmallCourseSync(courseLimit = 5) {
    try {
        console.log(`🧪 TESTING SMALL COURSE SYNC - ${courseLimit} COURSES ONLY`);
        console.log('=' .repeat(60));
        console.log('This process will:');
        console.log('1. Fetch course list from Docebo API');
        console.log(`2. Process ONLY the first ${courseLimit} courses`);
        console.log('3. For each course, get detailed course information');
        console.log('4. Create/update Docebo_Course__c record in Salesforce');
        console.log('5. Map all course fields properly');
        console.log('');
        console.log('🎯 This is a TEST RUN with limited courses to verify the process works');
        console.log('');

        const startTime = Date.now();

        // Step 1: Test Salesforce connection
        console.log('🔐 Testing Salesforce connection...');
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return false;
        }
        console.log('✅ Salesforce connection established');

        // Step 2: Fetch course list from Docebo
        console.log('📋 Fetching course list from Docebo...');
        const courseListedInfo = await doceboService.getTotalCourseListedInfo();
        
        if (!courseListedInfo || courseListedInfo.length === 0) {
            console.log('⚠️ No courses found in Docebo');
            return false;
        }

        console.log(`✅ Found ${courseListedInfo.length} total courses in Docebo`);

        // Step 3: Limit to test courses
        const coursesToProcess = courseListedInfo.slice(0, courseLimit);
        console.log(`🧪 Processing ${coursesToProcess.length} courses for testing`);

        let successCount = 0;
        let errorCount = 0;

        // Step 4: Process each course
        for (let i = 0; i < coursesToProcess.length; i++) {
            const course = coursesToProcess[i];
            const courseId = course.id;
            
            console.log(`\n📚 Processing course ${i + 1}/${coursesToProcess.length}: ${course.name || course.title} (ID: ${courseId})`);
            
            try {
                // Get detailed course information
                console.log(`   📋 Fetching detailed info for course ${courseId}...`);
                const courseInfo = await doceboService.getCourseInfo(courseId);
                
                if (courseInfo.status !== 200) {
                    console.error(`   ❌ Failed to fetch course info: ${courseInfo.status}`);
                    errorCount++;
                    continue;
                }

                console.log(`   ✅ Course info fetched successfully`);
                console.log(`   📝 Course: ${courseInfo.data.name || courseInfo.data.title}`);
                console.log(`   🏷️  Type: ${courseInfo.data.type || 'Unknown'}`);
                console.log(`   📊 Status: ${courseInfo.data.status || 'Unknown'}`);

                // Create/update course in Salesforce
                console.log(`   💾 Creating/updating course in Salesforce...`);
                const courseSaveRes = await createNewCourse(courseInfo.data);
                
                if (courseSaveRes) {
                    console.log(`   ✅ Course saved successfully in Salesforce`);
                    successCount++;
                } else {
                    console.error(`   ❌ Failed to save course in Salesforce`);
                    errorCount++;
                }

            } catch (error) {
                console.error(`   ❌ Error processing course ${courseId}:`, error.message);
                errorCount++;
            }
        }

        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);

        console.log('\n' + '='.repeat(60));
        console.log('🎉 SMALL COURSE SYNC TEST COMPLETED!');
        console.log(`⏱️  Total time: ${duration} seconds`);
        console.log(`✅ Successful: ${successCount} courses`);
        console.log(`❌ Errors: ${errorCount} courses`);
        console.log(`📊 Success rate: ${Math.round((successCount / coursesToProcess.length) * 100)}%`);
        console.log('');
        
        if (successCount > 0) {
            console.log('✅ Test completed successfully with course sync:');
            console.log('   • Course details fetched from Docebo API');
            console.log('   • Docebo_Course__c records created/updated in Salesforce');
            console.log('   • All course fields mapped properly');
            console.log('');
            console.log('🎯 If this test looks good, you can run the full course sync!');
        } else {
            console.log('⚠️ No courses were successfully synced. Please check the errors above.');
        }

        return successCount > 0;

    } catch (error) {
        console.error('❌ Small course sync test failed:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Parse command line arguments
const courseLimit = parseInt(process.argv[2]) || 5;

// Run the test
testSmallCourseSync(courseLimit).catch(console.error);
