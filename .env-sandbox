# Production Configuration
NODE_ENV=production
PORT=5000

# SSL is handled by Apache/Nginx vhost - Node.js runs HTTP only
# The web server proxies HTTPS requests to this HTTP port

# Webhook URLs - Updated with actual domain
DOCEBO_WEBHOOK_URL="https://communityreport.org"
SALESFORCE_WEBHOOK_URL="https://communityreport.org"
HUBSPOT_WEBHOOK_URL="https://communityreport.org"

# Docebo Api credentials to get access token
DOCEBO_API_BASE_URL = "https://strivetogether.docebosaas.com"
DOCEBO_API_SUB_DOMAIN="https://strivetogether.docebosaas.com/oauth2/token"
DOCEBO_API_CLIENT_NAME="sf-hs-dev"
DOCEBO_API_CLIENT_SECRET="****************************************************************"
DOCEBO_API_GRANT_TYPE="password"
DOCEBO_API_SCOPE="api"
DOCEBO_API_USER_NAME="<EMAIL>"
DOCEBO_API_PASSWORD="Connect2024!"

# SalesForce api credentials
SF_TOKEN_URL = "https://test.salesforce.com/services/oauth2/token"
SF_API_INSTANCE="https://strivetogether--full.sandbox.my.salesforce.com"
SF_API_CLIENT_ID="3MVG9ZqlCComSMtThZAVXpvedL0qQeQ3myKt.IAKWrX6WlgjQBPcWCyLEOCi27mzBxayz3Jv_xUMa1UiGgUPO"
SF_API_CLIENT_SECRET="****************************************************************"
SF_API_GRANT_TYPE="password"
SF_API_USER_NAME="<EMAIL>"
# SF_API_PASSWORD="qazxswQAZXSW10FqFUYlz1SydnwMjwS30stoeh"
SF_API_PASSWORD="6aRJmBSUcRPGv8D3cQp4tdrskpRGvrcQyonKN6C"
SF_API_ORG_PASS = "6aRJmBSUcRPGv8D"
SF_API_ACCESS_TOKEN="00DO4000002rak9!AQEAQBGhe8hPvFj82bLdGu.ojtmvvHGsQBF8ZvJwEM8nDaanoUTPsR8lYZkSEXrmCbHX4DfILjPf8oPd_HN4lLWD1bcrFiIA"
SF_API_ID="https://test.salesforce.com/id/00DO4000002rak9MAA/005O400000FAZCRIA5"

# HubSpot api credentials
HS_ACCESS_TOKEN = "********************************************"
HS_ACCESS_SECRET = "17e470f9-39e1-45f9-a81a-d59bfbe4b745"