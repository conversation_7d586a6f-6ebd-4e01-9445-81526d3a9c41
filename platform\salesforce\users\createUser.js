const getConnection = require("../common/getConnection");
const getSalesForceUserId = require("../common/getSalesForceUserId");

// Helper functions to map Docebo values to valid Salesforce picklist values
function mapRoleTypeToValidValue(doceboRoleType) {
    if (!doceboRoleType) return "";

    const roleMapping = {
        "Management": "Operations/Business Management",
        "Technical": "Programs",
        "Administrative": "Administrative",
        "Executive": "Executive Director",
        "Communications": "Communications",
        "Data": "Data and Research",
        "Policy": "Policy/Government",
        "Community": "Community Engagement/Organizing",
        "Fundraising": "Fundraising/Development",
        "Board": "Board of Directors"
    };

    return roleMapping[doceboRoleType] || "Other";
}

function mapRaceToValidValue(doceboRace) {
    if (!doceboRace) return "";

    const raceMapping = {
        "Asian": "Asian",
        "Black": "Black or African American",
        "African American": "Black or African American",
        "White": "White",
        "Hispanic": "Hispanic or Latine",
        "Latino": "Hispanic or Latine",
        "Latina": "Hispanic or Latine",
        "Latine": "Hispanic or Latine",
        "Native American": "American Indian or Alaskan Native",
        "Pacific Islander": "Native Hawaiian or Other Pacific Islander",
        "Multi-Racial": "Multi-Racial",
        "Mixed": "Multi-Racial",
        "Prefer not to say": "Prefer not to respond",
        "Prefer not to respond": "Prefer not to respond"
    };

    return raceMapping[doceboRace] || "Other";
}

function mapGenderToValidValue(doceboGender) {
    if (!doceboGender) return "";

    const genderMapping = {
        "Male": "Man",
        "Female": "Woman",
        "Man": "Man",
        "Woman": "Woman",
        "Non-binary": "Non-Binary or other gender identity",
        "Non-Binary": "Non-Binary or other gender identity",
        "Prefer not to say": "Prefer not to respond",
        "Prefer Not To Say": "Prefer not to respond"
    };

    return genderMapping[doceboGender] || "Prefer not to respond";
}

// Helper function to update existing Contact or Lead when Docebo_Users__c already exists
// Returns { updated: boolean, type: 'contact'|'lead'|null, id?: string }
async function updateExistingContactOrLead(conn, tmpUserInfo) {
    try {
        console.log(`🔍 Checking for existing Contact or Lead with email: ${tmpUserInfo.Email__c}`);

        // First check for existing Contact
        const existingContacts = await conn.sobject("Contact")
            .find({ Email: tmpUserInfo.Email__c })
            .limit(1)
            .execute();

        if (existingContacts.length > 0) {
            const contactId = existingContacts[0].Id;
            console.log(`📝 Updating existing Contact: ${contactId}`);

            const contactUpdateData = {
                Id: contactId,
                // Standard fields
                FirstName: tmpUserInfo.First_Name__c || null,
                LastName: tmpUserInfo.Last_Name__c || "Unknown",
                Email: tmpUserInfo.Email__c || null,
                Title: tmpUserInfo.Job_Title__c || "",

                // Flags and source
                Active_Portal_User__c: true,
                Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
                Created_by_Docebo_API__c: true,
                LeadSource: "Docebo Platform",
                Gateway__c: "Docebo API",

                // Full Contact mapping (align with Lead mapping using Contact field APIs)
                GenderIdentity: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c),
                Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
                Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
                Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),

                Description: `Docebo user - Level: ${tmpUserInfo.User_Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
                Languages__c: tmpUserInfo.Languages__c || "",

                // Mailing fields from DU city/state fallbacks
                mailingcity__c: tmpUserInfo.City__c || "",
                mailingstate__c: tmpUserInfo.State__c || "",

                Position_Role__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c) || "",

                Annual_Revenue__c: tmpUserInfo.Annual_Revenue__c || 0,
                Company__c: tmpUserInfo.Organization_Name__c || "",
                Industry__c: tmpUserInfo.Industry__c || "Not For Profit",
                Initiative__c: tmpUserInfo.Initiative__c || "",
                NumberOfEmployees__c: tmpUserInfo.NumberOfEmployees__c || 0,
                Organization_Employer__c: tmpUserInfo.Organization_Name__c || "",
                Rating__c: tmpUserInfo.Rating__c || "Warm",
                Time_Zone__c: tmpUserInfo.Time_Zone__c || "",
                Type__c: "Backbone Staff",
                Website__c: tmpUserInfo.Organization_URL__c || "",
                Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",

                Legacy_ID__c: String(tmpUserInfo.User_Unique_Id__c || ""),
                FTE__c: "Full-Time",
                Contact_Type__c: tmpUserInfo.Contact_Type__c || "Other"
            };

            const contactUpdateResult = await conn.sobject("Contact").update(contactUpdateData);
            if (contactUpdateResult.success) {
                console.log(`✅ Contact updated successfully: ${contactId}`);
                return { updated: true, type: 'contact', id: contactId };
            } else {
                console.error("❌ Contact update failed:", contactUpdateResult.errors);
                // continue to try Lead
            }
        }

        // If no Contact (or contact update failed), check for existing Lead
        const existingLeads = await conn.sobject("Lead")
            .find({ Email: tmpUserInfo.Email__c, IsConverted: false })
            .limit(1)
            .execute();

        if (existingLeads.length > 0) {
            const leadId = existingLeads[0].Id;
            console.log(`📝 Updating existing Lead: ${leadId}`);

            const leadUpdateData = {
                Id: leadId,
                FirstName: tmpUserInfo.First_Name__c || null,
                LastName: tmpUserInfo.Last_Name__c || "Unknown",
                Email: tmpUserInfo.Email__c || null,
                Company: tmpUserInfo.Organization_Name__c || "-",
                Title: tmpUserInfo.Job_Title__c || "",
                Website: tmpUserInfo.Organization_URL__c || "",
                Organization_URL__c: tmpUserInfo.Organization_URL__c || "",
                Status: "Open - Not Contacted",
                Created_by_Docebo_API__c: true,
                LeadSource: "Docebo Platform"
            };

            const leadUpdateResult = await conn.sobject("Lead").update(leadUpdateData);
            if (leadUpdateResult.success) {
                console.log(`✅ Lead updated successfully: ${leadId}`);
                return { updated: true, type: 'lead', id: leadId };
            } else {
                console.error("❌ Lead update failed:", leadUpdateResult.errors);
            }
        }

        console.log(`ℹ️ No existing Contact or Lead found for email: ${tmpUserInfo.Email__c}`);
        return { updated: false, type: null };

    } catch (err) {
        console.error("❌ Error in updateExistingContactOrLead:", err);
        return { updated: false, type: null };
    }
}

// Helper: set back-reference on Lead to point to Docebo_Users__c
async function setLeadBackReference(conn, leadId, doceboUserId) {
    try {
        if (!leadId || !doceboUserId) {
            console.log("ℹ️ Missing ids for back-reference, skipping", { leadId, doceboUserId });
            return false;
        }
        // Verify field exists before attempting update
        const leadDescribe = await conn.sobject("Lead").describe();
        const duLookup = leadDescribe.fields.find(f => f.name === 'Docebo_User__c');
        if (!duLookup) {
            console.log("ℹ️ Lead.Docebo_User__c field not found; skipping back-reference");
            return false;
        }
        const res = await conn.sobject("Lead").update({ Id: leadId, Docebo_User__c: doceboUserId });
        if (res.success) {
            console.log(`✅ Lead ${leadId} back-referenced to Docebo_Users__c ${doceboUserId}`);
            return true;
        } else {
            console.error("❌ Failed to set Lead.Docebo_User__c:", res.errors);
            return false;
        }
    } catch (e) {
        const msg = e && (e.message || e.errorCode || e.toString());
        console.error("❌ Error setting Lead back-reference:", msg);
        return false;
    }
}

// Helper: set back-reference on Contact to point to Docebo_Users__c
async function setContactBackReference(conn, contactId, doceboUserId) {
    try {
        if (!contactId || !doceboUserId) {
            console.log("ℹ️ Missing ids for Contact back-reference, skipping", { contactId, doceboUserId });
            return false;
        }
        const contactDescribe = await conn.sobject("Contact").describe();
        const duLookup = contactDescribe.fields.find(f => f.name === 'Docebo_User__c');
        if (!duLookup) {
            console.log("ℹ️ Contact.Docebo_User__c field not found; skipping back-reference");
            return false;
        }
        const res = await conn.sobject("Contact").update({ Id: contactId, Docebo_User__c: doceboUserId });
        if (res.success) {
            console.log(`✅ Contact ${contactId} back-referenced to Docebo_Users__c ${doceboUserId}`);
            return true;
        } else {
            console.error("❌ Failed to set Contact.Docebo_User__c:", res.errors);
            return false;
        }
    } catch (e) {
        const msg = e && (e.message || e.errorCode || e.toString());
        console.error("❌ Error setting Contact back-reference:", msg);
        return false;
    }
}


async function createNewUser(userInfo, userListedInfo) {
    // First check if user already exists by ID
    let userExistRes = await getSalesForceUserId(userInfo.user_data.user_id);

    // Prepare user data
    let tmpUserInfo = tidyData(userInfo, userListedInfo);

    // Ensure LastName is set for Salesforce Lead creation
    if (!tmpUserInfo.Last_Name__c || tmpUserInfo.Last_Name__c.trim() === "") {
        tmpUserInfo.Last_Name__c = "Unknown";
    }

    console.log("====== get connection for create/update User ======");
    const conn = await getConnection();
    if (!conn || !conn.accessToken) {
        console.error("Invalid Salesforce connection in createNewUser");
        return false;
    }

    // If user already exists, update Docebo_Users__c AND check for Lead/Contact to update
    if (userExistRes !== null) {
        console.log("User already exists in Salesforce, updating: ", userInfo.user_data.user_id);

        // Set the ID for update
        tmpUserInfo.Id = userExistRes;

        try {
            // Update Docebo_Users__c record
            const updateResult = await conn.sobject("Docebo_Users__c").update(tmpUserInfo);
            if (updateResult.success) {
                console.log("Docebo_Users__c updated successfully:", userInfo.user_data.user_id);

                // Re-enabled: check and update existing Contact/Lead by email (minimal safe fields)
                const updByUnique = await updateExistingContactOrLead(conn, tmpUserInfo);
                if (updByUnique && updByUnique.updated && updByUnique.id) {
                    try {
                        if (updByUnique.type === 'lead') {
                            const linkRes = await conn.sobject("Docebo_Users__c").update({ Id: tmpUserInfo.Id, Lead__c: updByUnique.id });
                            if (linkRes.success) { await setLeadBackReference(conn, updByUnique.id, tmpUserInfo.Id); }
                        } else if (updByUnique.type === 'contact') {
                            try {
                                const contactRec = await conn.sobject("Contact").findOne({ Id: updByUnique.id });
                                if (contactRec && contactRec.Email && tmpUserInfo.Email__c !== contactRec.Email) {
                                    try { await conn.sobject("Docebo_Users__c").update({ Id: tmpUserInfo.Id, Email__c: contactRec.Email }); } catch {}
                                }
                            } catch {}
                            await conn.sobject("Docebo_Users__c").update({ Id: tmpUserInfo.Id, Contact__c: updByUnique.id, Email__c: tmpUserInfo.Email__c });
                            try { await setContactBackReference(conn, updByUnique.id, tmpUserInfo.Id); } catch {}
                        }
                    } catch (assocErr) { console.log("ℹ️ Association after update failed:", assocErr && (assocErr.message || assocErr.toString())); }
                }

                return true;
            } else {
                console.error("Docebo_Users__c update failed:", updateResult.errors);
                return false;
            }
        } catch (err) {
            console.error("Error updating Docebo_Users__c:", err);
            return false;
        }
    }

    // If we get here, user doesn't exist by ID, but let's double-check by User_Unique_Id__c
    try {
        // First check if a user with this User_Unique_Id__c already exists
        const uniqueId = parseInt(userInfo.user_data.user_id, 10);
        if (!isNaN(uniqueId)) {
            console.log(`Double-checking if user with User_Unique_Id__c=${uniqueId} exists`);
            const existingUser = await conn.sobject("Docebo_Users__c")
                .findOne({ User_Unique_Id__c: uniqueId });

            if (existingUser) {
                console.log(`Found user by User_Unique_Id__c: ${existingUser.Id}`);
                // Update the existing user
                tmpUserInfo.Id = existingUser.Id;
                const updateResult = await conn.sobject("Docebo_Users__c").update(tmpUserInfo);
                if (updateResult.success) {
                    console.log(`Docebo_Users__c updated successfully by User_Unique_Id__c: ${uniqueId}`);

                    // Re-enabled: check and update existing Contact/Lead by email (minimal safe fields)
                    const upd = await updateExistingContactOrLead(conn, tmpUserInfo);
                    if (upd && upd.updated && upd.id) {
                        try {
                            if (upd.type === 'lead') {
                                const linkRes = await conn.sobject("Docebo_Users__c").update({ Id: tmpUserInfo.Id, Lead__c: upd.id });
                                if (linkRes.success) {
                                    await setLeadBackReference(conn, upd.id, tmpUserInfo.Id);
                                }
                            } else if (upd.type === 'contact') {
                                try {
                                    const contactRec = await conn.sobject("Contact").findOne({ Id: upd.id });
                                    if (contactRec && contactRec.Email && tmpUserInfo.Email__c !== contactRec.Email) {
                                        try { await conn.sobject("Docebo_Users__c").update({ Id: tmpUserInfo.Id, Email__c: contactRec.Email }); } catch {}
                                    }
                                } catch {}
                                await conn.sobject("Docebo_Users__c").update({ Id: tmpUserInfo.Id, Contact__c: upd.id, Email__c: tmpUserInfo.Email__c });
                                try { await setContactBackReference(conn, upd.id, tmpUserInfo.Id); } catch {}
                            }
                        } catch (assocErr) {
                            console.log("ℹ️ Association after update failed:", assocErr && (assocErr.message || assocErr.toString()));
                        }
                    }

                    return true;
                } else {
                    console.error(`Failed to update user by User_Unique_Id__c: ${uniqueId}`, updateResult.errors);
                    return false;
                }
            }
        }

        // If we get here, user really doesn't exist, so create a new one
        if (conn.accessToken) {

            // Pre-create Contact/Lead update removed; we now always create Docebo_Users__c first and link afterwards.


            // COMMENTED OUT: Contact update logic
            /*
            const existingContacts = await conn.sobject("Contact")
                .find({ Email: tmpUserInfo.Email__c })
                .execute();
            if (existingContacts.length > 0) {
                const contactId = existingContacts[0].Id;
                const contactUpdateData = {
                    Id: contactId,
                    LastName: tmpUserInfo.Last_Name__c,
                    FirstName: tmpUserInfo.First_Name__c,
                    Email: tmpUserInfo.Email__c,
                    Title: tmpUserInfo.Job_Title__c || "",

                    // FIX: Add comprehensive Contact field mappings
                    Created_by_Docebo_API__c: true,
                    GenderIdentity: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c), // FIX: Use correct field name
                    Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
                    Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
                    Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),

                    // Additional fields with correct names
                    Description: `Docebo user - Level: ${tmpUserInfo.User_Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
                    Fax: getAdditionalData(userInfo.additional_fields || [], "26") || "",
                    Phone: userInfo.user_data.phone || "",
                    Languages__c: userInfo.user_data.language || "",
                    mailingcity__c: getAdditionalData(userInfo.additional_fields || [], "24") || "", // FIX: Correct field name
                    mailingcountry__c: getAdditionalData(userInfo.additional_fields || [], "28") || "", // FIX: Correct field name
                    mailingpostalcode__c: getAdditionalData(userInfo.additional_fields || [], "29") || "", // FIX: Correct field name
                    mailingstate__c: getStateLabel(userInfo.additional_fields || [], "25") || "", // FIX: Correct field name
                    mailingstreet__c: getAdditionalData(userInfo.additional_fields || [], "30") || "", // FIX: Correct field name
                    Position_Role__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c) || "",

                    // FIX: Add missing required fields
                    Annual_Revenue__c: tmpUserInfo.Annual_Revenue__c || 0,
                    Company__c: tmpUserInfo.Organization_Name__c || "",
                    Industry__c: tmpUserInfo.Industry__c || "Not For Profit",
                    Initiative__c: tmpUserInfo.Initiative__c || "",
                    LeadSource: "Docebo Platform",
                    NumberOfEmployees__c: tmpUserInfo.NumberOfEmployees__c || 0,
                    Organization_Employer__c: tmpUserInfo.Organization_Name__c || "",
                    Rating__c: tmpUserInfo.Rating__c || "Warm",
                    Time_Zone__c: userInfo.user_data.timezone || "",
                    Type__c: "Backbone Staff",
                    Website__c: tmpUserInfo.Organization_URL__c || "",
                    Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",

                    Active_Portal_User__c: tmpUserInfo.Email_Validation_Status__c || false,
                    FTE__c: "Full-Time",
                    Gateway__c: "Docebo API",
                    Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
                    Legacy_ID__c: String(tmpUserInfo.User_Unique_Id__c || ""), // FIX: Correct field name
                    No_Longer_Leadership__c: false,
                    No_Longer_Staff__c: false,
                    Number_of_years_in_the_partnership__c: "0", // FIX: Correct field name
                    Contact_Type__c: "Other"
                };
                const updateResult = await conn.sobject("Contact").update(contactUpdateData);
                if (updateResult.success) {
                    tmpUserInfo.Contact__c = contactId;
                }
            } else {
            */

            // Skip Contact checking and proceed directly to Lead creation (Account creation removed)
            {
                // REMOVED: Account creation - we only want Lead and Docebo_Users__c
                // const accountData = { ... };
                // const accountResult = await conn.sobject("Account").create(accountData);

                // Proceed directly to Lead creation without Account
                {
                    // tmpUserInfo.Account__c = accountResult.id; // Field doesn't exist on Docebo_Users__c
                    // Create lead data with ALL the same fields that contacts have
                    // These fields should be created on the Lead object to match Contact capabilities
                    const leadData = {
                        // Standard Lead fields mapped according to your specifications
                        LastName: tmpUserInfo.Last_Name__c && tmpUserInfo.Last_Name__c.trim() !== "" ? tmpUserInfo.Last_Name__c : "Unknown",
                        FirstName: tmpUserInfo.First_Name__c,
                        Email: tmpUserInfo.Email__c,
                        Company: tmpUserInfo.Organization_Name__c || "-",
                        Title: tmpUserInfo.Job_Title__c || "",
                        Website: tmpUserInfo.Organization_URL__c || "",
                        Organization_URL__c: tmpUserInfo.Organization_URL__c || "",
                        Status: "Open - Not Contacted",

                        // Custom fields mapped according to your field mapping list
                        Created_by_Docebo_API__c: true,
                        Gender__c: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c),
                        Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
                        Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
                        Race_Identity__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),

                        // Additional fields from your mapping - now with proper data
                        Description: `Docebo user - Level: ${tmpUserInfo.User_Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
                        Fax: getAdditionalData(userInfo.additional_fields || [], "26") || "", // Map from additional field if available
                        Salutation: getAdditionalData(userInfo.additional_fields || [], "27") || "", // Map from additional field if available
                        Phone: userInfo.user_data.phone || "", // FIX: Use actual phone from user_data
                        Languages__c: userInfo.user_data.language || "", // FIX: Use actual language from user_data
                        mailingcity__c: getAdditionalData(userInfo.additional_fields || [], "24") || "", // FIX: Correct field name
                        mailingcountry__c: getAdditionalData(userInfo.additional_fields || [], "28") || "", // FIX: Correct field name
                        mailingpostalcode__c: getAdditionalData(userInfo.additional_fields || [], "29") || "", // FIX: Correct field name
                        mailingstate__c: getStateLabel(userInfo.additional_fields || [], "25") || "", // FIX: Correct field name
                        mailingstreet__c: getAdditionalData(userInfo.additional_fields || [], "30") || "", // FIX: Correct field name
                        position_role__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c) || "", // FIX: Correct field name
                        LeadSource: "Docebo Platform",

                        // FIX: Add missing required fields
                        accountid__c: tmpUserInfo.Account_ID__c || "",
                        AnnualRevenue: tmpUserInfo.Annual_Revenue__c || 0, // Lead uses standard AnnualRevenue field
                        Industry: tmpUserInfo.Industry__c || "Not For Profit", // Default to common value
                        Initiative__c: tmpUserInfo.Initiative__c || "",
                        NumberOfEmployees: tmpUserInfo.NumberOfEmployees__c || 0,
                        Organization_employer__c: tmpUserInfo.Organization_Name__c || "",
                        Rating: tmpUserInfo.Rating__c || "Warm", // Default rating
                        Time_Zone__c: tmpUserInfo.Time_Zone__c || "",
                        Type__c: "Backbone Staff", // Default type
                        Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",
                        // REMOVED: These fields don't exist on Lead object
                        // Annual_Revenue__c: tmpUserInfo.Annual_Revenue__c || 0,
                        // Industry__c: tmpUserInfo.Industry__c || "",
                        // NumberOfEmployees__c: tmpUserInfo.NumberOfEmployees__c || 0,
                        // Rating__c: tmpUserInfo.Rating__c || "",
                        // TimeZone: userInfo.user_data.timezone || "",
                        // Account and system fields (accountid field doesn't exist on Lead)
                        Active_Portal_User__c: tmpUserInfo.Email_Validation_Status__c || false,
                        FTE__c: "Full-Time", // Default value
                        Gateway__c: "Docebo API",
                        Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
                        Legacy_Id__c: String(tmpUserInfo.User_Unique_Id__c || ""),
                        No_Longer_Leadership__c: false, // Default value
                        No_Longer_Staff__c: false, // Default value
                        Number_of_Years_in_the_Partnership__c: 0, // Not available in Docebo data
                        //OwnerId: tmpUserInfo.OwnerId || "005O400000BxnnxIAB", - Not needed
                        Contact_Type__c: "Other"
                    };

                    // Enhanced logging for Lead creation
                    console.log(`🎯 CREATING LEAD for User ${tmpUserInfo.User_Unique_Id__c}:`);
                    console.log("Lead data being created:", JSON.stringify(leadData, null, 2));

                    // Log key Lead fields for verification
                    console.log("📊 KEY LEAD FIELDS:");
                    console.log(`   Company: "${leadData.Company}"`);
                    console.log(`   Title: "${leadData.Title}"`);
                    console.log(`   Languages__c: "${leadData.Languages__c}"`);
                    console.log(`   mailingcity__c: "${leadData.mailingcity__c}"`); // FIX: Use correct field name
                    console.log(`   mailingstate__c: "${leadData.mailingstate__c}"`); // FIX: Use correct field name
                    console.log(`   Gender__c: "${leadData.Gender__c}"`);
                    console.log(`   Race_Identity__c: "${leadData.Race_Identity__c}"`);
                    console.log(`   Role_Type__c: "${leadData.Role_Type__c}"`);

                    // Proceed with Lead creation and associations (filter issues resolved)
                    var pendingLeadData = leadData;
                }
            }
            console.log("====== Create Object ======");

            try {
                const result = await conn.sobject("Docebo_Users__c").create(tmpUserInfo);
                if (result.success) {
                    console.log(`User created successfully: ${userInfo.user_data.user_id}`);

                    // Proceed with Lead creation and association (filter issues resolved)
                    var createdLeadId = null;

                    // ENABLED: Lead creation logic for testing (skip if matched existing Contact/Lead)
                    if (!skipLeadCreation && typeof pendingLeadData !== 'undefined' && pendingLeadData) {
                        try {
                            console.log(`🔄 Creating Lead AFTER successful user creation for ${tmpUserInfo.Email__c}`);

                            // Check for existing Lead before creating to prevent duplicates
                            const existingLeads = await conn.sobject("Lead")
                                .find({
                                    Email: tmpUserInfo.Email__c,
                                    IsConverted: false
                                })
                                .limit(1)
                                .execute();

                            if (existingLeads.length > 0) {
                                console.log(`✅ Found existing Lead for ${tmpUserInfo.Email__c}: ${existingLeads[0].Id}`);
                                createdLeadId = existingLeads[0].Id;
                                console.log(`🔄 Will reuse existing Lead instead of creating duplicate`);
                            } else {
                                const leadResult = await conn.sobject("Lead").create(pendingLeadData);
                                if (leadResult.success) {
                                    console.log(`✅ Lead created successfully AFTER user creation: ${leadResult.id}`);
                                    createdLeadId = leadResult.id;
                                } else {
                                    console.error("❌ Lead creation failed:", leadResult.errors);
                                }
                            }
                        } catch (leadError) {
                            console.error("❌ Error creating lead after user creation:", leadError);
                        }
                    }


                        // After creating Docebo_Users__c, check for existing Contact/Lead and update/link accordingly
                        let skipLeadCreation = false;
                        try {
                            const updAfterCreate = await updateExistingContactOrLead(conn, tmpUserInfo);
                            if (updAfterCreate && updAfterCreate.updated && updAfterCreate.id) {
                                if (updAfterCreate.type === 'contact') {
                                    try {
                                        // Normalize DU email to match Contact email before linking (for lookup filter rules)
                                        let contactRec = await conn.sobject("Contact").findOne({ Id: updAfterCreate.id });
                                        if (contactRec && contactRec.Email && tmpUserInfo.Email__c !== contactRec.Email) {
                                            try { await conn.sobject("Docebo_Users__c").update({ Id: result.id, Email__c: contactRec.Email }); } catch {}
                                        }
                                        const linkContactRes = await conn.sobject("Docebo_Users__c").update({ Id: result.id, Contact__c: updAfterCreate.id, Email__c: (contactRec && contactRec.Email) || tmpUserInfo.Email__c });
                                        if (linkContactRes.success) {
                                            console.log(`✅ Linked Docebo_Users__c ${result.id} to existing Contact ${updAfterCreate.id}`);
                                            await setContactBackReference(conn, updAfterCreate.id, result.id);
                                            skipLeadCreation = true; // Since Contact exists, no need to create a Lead
                                        } else {
                                            console.warn("⚠️ Failed to link DU to existing Contact:", linkContactRes.errors);
                                        }
                                    } catch (linkContactErr) {
                                        console.warn("⚠️ Error linking DU to existing Contact:", linkContactErr && (linkContactErr.message || linkContactErr.toString()));
                                    }
                                } else if (updAfterCreate.type === 'lead') {
                                    try {
                                        const linkLeadRes = await conn.sobject("Docebo_Users__c").update({ Id: result.id, Lead__c: updAfterCreate.id, Email__c: tmpUserInfo.Email__c });
                                        if (linkLeadRes.success) {
                                            console.log(`✅ Linked Docebo_Users__c ${result.id} to existing Lead ${updAfterCreate.id}`);
                                            await setLeadBackReference(conn, updAfterCreate.id, result.id);
                                            skipLeadCreation = true; // Existing Lead found; skip creating a new one
                                        } else {
                                            console.warn("⚠️ Failed to link DU to existing Lead:", linkLeadRes.errors);
                                        }
                                    } catch (linkLeadErr) {
                                        console.warn("⚠️ Error linking DU to existing Lead:", linkLeadErr && (linkLeadErr.message || linkLeadErr.toString()));
                                    }
                                }
                            }
                        } catch (postCreateUpdErr) {
                            console.log("ℹ️ Post-create updateExistingContactOrLead failed:", postCreateUpdErr && (postCreateUpdErr.message || postCreateUpdErr.toString()));
                        }

                    // ENABLED: Lead association logic
                    if (createdLeadId) { // Enable Lead association
                        try {
                            const maxAttempts = 5;
                            let lastError = null;
                            for (let attempt = 1; attempt <= maxAttempts; attempt++) {
                                const backoffMs = attempt * 500;
                                await new Promise(res => setTimeout(res, backoffMs));
                                console.log(`🔗 Attempt ${attempt}/${maxAttempts} to associate DU → Lead (waiting ${backoffMs}ms)`);

                                // Re-lookup Lead by Id; if not found, fallback to email lookup (non-converted only)
                                let leadRecord = await conn.sobject("Lead").findOne({ Id: createdLeadId });
                                if (!leadRecord) {
                                    leadRecord = await conn.sobject("Lead").findOne({ Email: tmpUserInfo.Email__c, IsConverted: false });
                                    if (leadRecord) {
                                        console.log(`ℹ️ Lead re-identified by Email: ${leadRecord.Id}`);
                                        createdLeadId = leadRecord.Id;
                                    }
                                }

                                if (!leadRecord) {
                                    console.log("⚠️ Lead not found yet, will retry...");
                                    continue;
                                }

                                // Pre-check: log DU vs Lead emails and key fields relevant to lookup filter
                                let duEmail = null;
                                let leadEmail = null;
                                try {
                                    const duRec = await conn.sobject("Docebo_Users__c").findOne({ Id: result.id });
                                    duEmail = (duRec && duRec.Email__c) ? duRec.Email__c : null;
                                    leadEmail = leadRecord.Email || null;
                                    const emailsEqual = (duEmail || '') === (leadEmail || '');
                                    console.log(`🔬 Association pre-check: DU.Email__c='${duEmail}' (len=${duEmail ? duEmail.length : 0}) | Lead.Email='${leadEmail}' (len=${leadEmail ? leadEmail.length : 0}) | ExactEqual=${emailsEqual}`);
                                    console.log(`🔬 Lead fields: Status=${leadRecord.Status}, LeadSource=${leadRecord.LeadSource}, Contact_Type__c=${leadRecord.Contact_Type__c}, Type__c=${leadRecord.Type__c}, Company=${leadRecord.Company}`);
                                } catch (preLogErr) {
                                    console.log("ℹ️ Could not run association pre-check:", preLogErr.message);
                                }

                                // Normalize and align source Email__c to match Lead.Email exactly (handles whitespace/case diffs)
                                try {
                                    if ((duEmail || '') !== (leadEmail || '')) {
                                        console.log("🧽 Normalizing DU.Email__c to match Lead.Email before linking...");
                                        await conn.sobject("Docebo_Users__c").update({
                                            Id: result.id,
                                            Email__c: leadEmail || duEmail // prefer Lead email if present
                                        });
                                        duEmail = leadEmail || duEmail;
                                    }
                                } catch (normErr) {
                                    console.log("ℹ️ Could not normalize Email__c prior to link:", normErr.message);
                                }

                                // Include Email__c explicitly to ensure lookup filter evaluates with the normalized value
                                const linkResult = await conn.sobject("Docebo_Users__c").update({
                                    Id: result.id,
                                    Lead__c: createdLeadId,
                                    Email__c: duEmail
                                });

                                if (linkResult.success) {
                                    console.log(`✅ Docebo_Users__c ${result.id} linked to Lead ${createdLeadId}`);
                                    // Also set back-reference on Lead if possible
                                    await setLeadBackReference(conn, createdLeadId, result.id);
                                    lastError = null;
                                    break;
                                } else {
                                    lastError = linkResult.errors;
                                    const firstErr = Array.isArray(linkResult.errors) ? linkResult.errors[0] : linkResult.errors;
                                    console.warn("⚠️ Association attempt failed:", firstErr);

                                    // Describe the Lead and DU fields we rely on to help diagnose lookup filters
                                    try {
                                        const duDescribe = await conn.sobject("Docebo_Users__c").describe();
                                        const leadLookupField = duDescribe.fields.find(f => f.name === 'Lead__c');
                                        if (leadLookupField) {
                                            console.log("🔎 Lead__c field describe:", {
                                                type: leadLookupField.type,
                                                referenceTo: leadLookupField.referenceTo,
                                                nillable: leadLookupField.nillable,
                                                filterable: leadLookupField.filterable,
                                                relationshipName: leadLookupField.relationshipName
                                            });
                                        }
                                    } catch (descErr) {
                                        console.log("ℹ️ Could not describe Docebo_Users__c.Lead__c:", descErr.message);
                                    }

                                    const ffve = (firstErr && ((firstErr.message || firstErr.errorCode || "").toString().includes("FIELD_FILTER_VALIDATION_EXCEPTION")));
                                    if (ffve) {
                                        console.warn("⚠️ Lookup filter blocked association (FIELD_FILTER_VALIDATION_EXCEPTION). Will retry if attempts remain.");
                                    } else {
                                        console.error("❌ Failed to link Docebo_Users__c to Lead:", linkResult.errors);
                                    }
                                }
                            }
                            if (lastError) {
                                console.error("❌ Association failed after retries. Errors:", lastError);
                            }
                        } catch (linkError) {
                            console.error("❌ Error linking Docebo_Users__c to Lead:", linkError);

                            // Workaround disabled previously; leaving commented block for reference
                            /*
                            if (linkError.errorCode === 'FIELD_FILTER_VALIDATION_EXCEPTION') {
                                console.log(`🔄 Lead association blocked by lookup filter, trying Contact association...`);
                                await tryContactAssociationWorkaround(conn, result.id, tmpUserInfo, userInfo);
                            } else {
                                // Try alternative association method
                                await tryAlternativeLeadAssociation(conn, result.id, tmpUserInfo.Email__c);
                            }
                            */
                        }
                    }

                    return true;
                } else {
                    console.error(`User creation failed: ${userInfo.user_data.user_id}`, result.errors);
                    return false;
                }
            } catch (err) {
                // If creation fails due to duplicate, try to find and update the user
                if (err.errorCode === 'DUPLICATE_VALUE' && err.message.includes('User_Unique_Id__c duplicates value')) {
                    console.error(`Duplicate user detected during creation, attempting to update: ${userInfo.user_data.user_id}`);

                    // Extract the record ID from the error message
                    const match = err.message.match(/id: ([a-zA-Z0-9]+)/);
                    if (match && match[1]) {
                        const existingId = match[1];
                        console.log(`Found existing user ID from error: ${existingId}`);

                        // Update the existing record
                        tmpUserInfo.Id = existingId;
                        const updateResult = await conn.sobject("Docebo_Users__c").update(tmpUserInfo);
                        if (updateResult.success) {
                            console.log(`Docebo_Users__c updated after duplicate detection: ${userInfo.user_data.user_id}`);

                            // Previously disabled Contact/Lead update; continue with association attempt
                            if (createdLeadId) {
                                try {
                                    // First verify the Lead still exists
                                    const leadExists = await conn.sobject("Lead").findOne({ Id: createdLeadId });
                                    if (leadExists) {
                                        const linkResult = await conn.sobject("Docebo_Users__c").update({
                                            Id: existingId,
                                            Lead__c: createdLeadId
                                        });

                                        if (linkResult.success) {
                                            console.log(`✅ Duplicate user ${existingId} linked to Lead ${createdLeadId}`);
                                            await setLeadBackReference(conn, createdLeadId, existingId);
                                        } else {
                                            console.log(`⚠️ Could not link duplicate user to Lead:`, linkResult.errors);

                                            // Workaround disabled previously; leaving commented block for reference
                                            /*
                                            if (linkResult.errors && linkResult.errors.some(e => e.statusCode === 'FIELD_FILTER_VALIDATION_EXCEPTION')) {
                                                console.log(`🔄 Lead association blocked by lookup filter, trying Contact association...`);
                                                await tryContactAssociationWorkaround(conn, existingId, tmpUserInfo, userInfo);
                                            }
                                            */
                                        }
                                    } else {
                                        console.log(`⚠️ Lead ${createdLeadId} no longer exists, skipping association`);
                                    }
                                } catch (linkError) {
                                    console.log(`⚠️ Could not link duplicate user to Lead: ${linkError.message}`);

                                    // Workaround disabled previously; leaving commented block for reference
                                    /*
                                    if (linkError.errorCode === 'FIELD_FILTER_VALIDATION_EXCEPTION') {
                                        console.log(`🔄 Lead association blocked by lookup filter, trying Contact association...`);
                                        await tryContactAssociationWorkaround(conn, existingId, tmpUserInfo, userInfo);
                                    }
                                    */
                                }
                            }

                            return true;
                        }
                    }
                }
                console.error(`Error creating user: ${userInfo.user_data.user_id}`, err);
                return false;
            }
        }
        return false;
    } catch (err) {
        console.error(`Unexpected error in createNewUser: ${err.message}`);
        return false;
    }
}

async function getTotalUserList() {
    const conn = await getConnection();
    if (!conn.accessToken) {
        console.error("No valid Salesforce connection.");
        return [];
    }

    try {
        let allUsers = [];
        let result = await conn.query(
            "SELECT User_Unique_Id__c FROM Docebo_Users__c WHERE User_Unique_Id__c != null"
        );

        allUsers.push(...result.records);

        while (!result.done) {
            result = await conn.queryMore(result.nextRecordsUrl);
            allUsers.push(...result.records);
        }

        return allUsers.map(user => user.User_Unique_Id__c);
    } catch (err) {
        console.error("Error fetching all Salesforce users:", err);
        return [];
    }
}

async function saveUsersInBatch(users) {
    const batchSize = 200;
    const conn = await getConnection();

    if (!conn.accessToken) {
        console.error("No valid Salesforce connection.");
        return false;
    }

    for (let i = 0; i < users.length; i += batchSize) {
        const batch = users.slice(i, i + batchSize);
        console.log(`Processing batch ${i / batchSize + 1}: ${batch.length} users`);

        const batchData = [];

        for (const user of batch) {
            try {
                const tmpUserInfo = tidyData(user.userInfo, user.userListedInfo);
                console.log("This is user info: ", user.userInfo);
                console.log("This is user listed info: ", user.userListedInfo);

                // Pre-create check removed: we always create Docebo_Users__c first, then link/update and decide on Lead creation afterwards.

                // COMMENTED OUT: Contact update logic for batch processing
                /*
                const existingContacts = await conn.sobject("Contact")
                    .find({ Email: tmpUserInfo.Email__c })
                    .execute();

                if (existingContacts.length > 0) {
                    const contactId = existingContacts[0].Id;
                    const contactUpdateData = {
                        Id: contactId,
                        LastName: tmpUserInfo.Last_Name__c,    // Standard field
                        FirstName: tmpUserInfo.First_Name__c,  // Standard field
                        Email: tmpUserInfo.Email__c,           // Standard field
                        Title: tmpUserInfo.Job_Title__c || "", // Standard field

                        // FIX: Use ONLY approved Contact fields
                        Created_by_Docebo_API__c: true,
                        GenderIdentity: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c),
                        Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
                        Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
                        Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),

                        // Additional fields with correct names
                        Description: `Docebo user - Level: ${tmpUserInfo.User_Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
                        Fax: "",
                        Phone: "",
                        Languages__c: "",
                        mailingcity__c: "", // FIX: Correct field name
                        mailingcountry__c: "", // FIX: Correct field name
                        mailingpostalcode__c: "", // FIX: Correct field name
                        mailingstate__c: "", // FIX: Correct field name
                        mailingstreet__c: "", // FIX: Correct field name
                        Position_Role__c: tmpUserInfo.Role_Type__c || "",

                        // FIX: Add missing required fields
                        Annual_Revenue__c: tmpUserInfo.Annual_Revenue__c || 0,
                        Company__c: tmpUserInfo.Organization_Name__c || "",
                        Industry__c: tmpUserInfo.Industry__c || "Not For Profit",
                        Initiative__c: tmpUserInfo.Initiative__c || "",
                        LeadSource: "Docebo Platform",
                        NumberOfEmployees__c: tmpUserInfo.NumberOfEmployees__c || 0,
                        Organization_Employer__c: tmpUserInfo.Organization_Name__c || "",
                        Rating__c: tmpUserInfo.Rating__c || "Warm",
                        Time_Zone__c: tmpUserInfo.Time_Zone__c || "",
                        Type__c: "Backbone Staff",
                        Website__c: tmpUserInfo.Organization_URL__c || "",
                        Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",

                        Active_Portal_User__c: tmpUserInfo.Email_Validation_Status__c || false,
                        FTE__c: "Full-Time",
                        Gateway__c: "Docebo API",
                        Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
                        Legacy_ID__c: String(tmpUserInfo.User_Unique_Id__c || ""), // FIX: Correct field name
                        No_Longer_Leadership__c: false,
                        No_Longer_Staff__c: false,
                        Number_of_years_in_the_partnership__c: "0", // FIX: Correct field name
                        Contact_Type__c: "Other"
                    };
                    const updateResult = await conn.sobject("Contact").update(contactUpdateData);
                    if (updateResult.success) {
                        tmpUserInfo.Contact__c = contactId;
                    }
                } else {
                */

                // Skip Contact checking and proceed directly to Lead creation (Account creation removed)
                {
                    // REMOVED: Account creation - we only want Lead and Docebo_Users__c
                    // const accountData = { ... };
                    // const accountResult = await conn.sobject("Account").create(accountData);

                    // Proceed directly to Lead creation without Account
                    {
                        // tmpUserInfo.Account__c = accountResult.id; // Field doesn't exist on Docebo_Users__c

                        const leadData = {
                            // Standard Lead fields
                            Company: tmpUserInfo.Company || "-",
                            Email: tmpUserInfo.Email__c,
                            Title: tmpUserInfo.Job_Title__c || "",
                            FirstName: tmpUserInfo.First_Name__c,
                            LastName: tmpUserInfo.Last_Name__c,
                            Website: tmpUserInfo.Organization_URL__c || "",
                                Organization_URL__c: tmpUserInfo.Organization_URL__c || "",

                            Status: "Open - Not Contacted",

                            // Custom fields from your exact field list
                            Created_by_Docebo_API__c: true,
                            Gender__c: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c),
                            Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
                            Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
                            Race_Identity__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),
                            Contact_Type__c: "Other",
                            Description: `Docebo user - Level: ${tmpUserInfo.User_Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
                            Fax: getAdditionalData(user.userInfo.additional_fields || [], "26") || "", // Map from additional field if available
                            Salutation: getAdditionalData(user.userInfo.additional_fields || [], "27") || "", // Map from additional field if available
                            Phone: user.userInfo.user_data.phone || "", // FIX: Use actual phone from user_data
                            Languages__c: user.userInfo.user_data.language || "", // FIX: Use actual language from user_data
                            mailingcity__c: getAdditionalData(user.userInfo.additional_fields || [], "24") || "", // FIX: Correct field name
                            mailingcountry__c: getAdditionalData(user.userInfo.additional_fields || [], "28") || "", // FIX: Correct field name
                            mailingpostalcode__c: getAdditionalData(user.userInfo.additional_fields || [], "29") || "", // FIX: Correct field name
                            mailingstate__c: getStateLabel(user.userInfo.additional_fields || [], "25") || "", // FIX: Correct field name
                            mailingstreet__c: getAdditionalData(user.userInfo.additional_fields || [], "30") || "", // FIX: Correct field name
                            position_role__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c) || "", // FIX: Correct field name
                            LeadSource: "Docebo Platform",

                            // FIX: Add missing required fields for Lead (using correct field names)
                            AnnualRevenue: tmpUserInfo.Annual_Revenue__c || 0, // Lead uses standard AnnualRevenue field
                            Industry: tmpUserInfo.Industry__c || "Not For Profit",
                            Initiative__c: tmpUserInfo.Initiative__c || "",
                            NumberOfEmployees: tmpUserInfo.NumberOfEmployees__c || 0,
                            Rating: tmpUserInfo.Rating__c || "Warm",
                            Time_Zone__c: user.userInfo.user_data.timezone || "",

                            Active_Portal_User__c: tmpUserInfo.Email_Validation_Status__c || false,
                            FTE__c: "Full-Time",
                            Gateway__c: "Docebo API",
                            Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
                            Legacy_Id__c: String(tmpUserInfo.User_Unique_Id__c || ""),
                            No_Longer_Leadership__c: false,
                            No_Longer_Staff__c: false,
                            Number_of_Years_in_the_Partnership__c: 0,
                            // OwnerId: tmpUserInfo.OwnerId || "005O400000BxnnxIAB",

                        };

                        // Defer lead creation: push to pending list; we'll create only if no existing Contact/Lead matched after DU creation
                        leadsPending.push(leadData);
                    }
                }

                batchData.push({
                    attributes: { type: "Docebo_Users__c" },
                    ...tmpUserInfo
                });

            } catch (err) {
                console.error(`Error processing user ${user.userInfo?.user_data?.user_id}:`, err);
            }
        }

        try {
            const result = await conn.sobject("Docebo_Users__c").create(batchData, { allOrNone: false });

            result.forEach(async (res, index) => {
                if (res.success) {
                    console.log(`User ${batch[index].userInfo.user_data.user_id} saved successfully.`);

                    // After DU create: update/link to existing Contact/Lead if present
                    try {
                        const upd = await updateExistingContactOrLead(conn, batchData[index]);
                        if (upd && upd.updated && upd.id) {
                            if (upd.type === 'contact') {
                                // Normalize DU email to match Contact email before linking
                                let contactRec = await conn.sobject("Contact").findOne({ Id: upd.id });
                                if (contactRec && contactRec.Email && batchData[index].Email__c !== contactRec.Email) {
                                    try { await conn.sobject("Docebo_Users__c").update({ Id: res.id, Email__c: contactRec.Email }); } catch {}
                                }
                                // Link DU to Contact and skip lead creation
                                const linkRes = await conn.sobject("Docebo_Users__c").update({ Id: res.id, Contact__c: upd.id, Email__c: (contactRec && contactRec.Email) || batchData[index].Email__c });
                                if (linkRes.success) {
                                    console.log(`✅ Batch: Docebo_Users__c ${res.id} linked to Contact ${upd.id}`);
                                    await setContactBackReference(conn, upd.id, res.id);
                                    return;
                                } else {
                                    console.warn("⚠️ Batch: Failed to link DU to Contact:", linkRes.errors);
                                }
                            } else if (upd.type === 'lead') {
                                const linkRes = await conn.sobject("Docebo_Users__c").update({ Id: res.id, Lead__c: upd.id, Email__c: batchData[index].Email__c });
                                if (linkRes.success) {
                                    console.log(`✅ Batch: Docebo_Users__c ${res.id} linked to Lead ${upd.id}`);
                                    await setLeadBackReference(conn, upd.id, res.id);
                                    return;
                                } else {
                                    console.warn("⚠️ Batch: Failed to link DU to Lead:", linkRes.errors);
                                }
                            }
                        }
                    } catch (postUpdErr) {
                        console.log("ℹ️ Batch: Post-create updateExistingContactOrLead failed:", postUpdErr && (postUpdErr.message || postUpdErr.toString()));
                    }

                    // If no existing Contact/Lead matched, create a new Lead now
                    try {
                        const leadResult = await conn.sobject("Lead").create(leadsPending[index]);
                        if (leadResult.success) {
                            console.log(`✅ Batch: Lead created ${leadResult.id}`);
                            const linkResult = await conn.sobject("Docebo_Users__c").update({ Id: res.id, Lead__c: leadResult.id, Email__c: batchData[index].Email__c });
                            if (linkResult.success) {
                                console.log(`✅ Batch: Docebo_Users__c ${res.id} linked to Lead ${leadResult.id}`);
                                await setLeadBackReference(conn, leadResult.id, res.id);
                            } else {
                                console.error("❌ Batch: Failed to link Docebo_Users__c to new Lead:", linkResult.errors);
                            }
                        } else {
                            console.error("❌ Batch: Lead creation failed:", leadResult.errors);
                        }
                    } catch (leadCreateErr) {
                        console.error("❌ Batch: Error creating Lead:", leadCreateErr);
                    }
                } else {
                    console.error(`Failed to save user ${batch[index].userInfo.user_data.user_id}:`, res.errors);
                }
            });
        } catch (err) {
            console.error("Error during batch save to Docebo_Users__c:", err);
        }
    }
}

let saveUserInfo = {
    // ONLY FIELDS THAT ACTUALLY EXIST ON DOCEBO_USERS__C:
    "User_Unique_Id__c": 0,
    "User_Level__c": "",
    "Deactivated__c": false,

    "User_Creation_Date__c": "",
    "User_Expiration_Date__c": "",
    "User_Last_Access_Date__c": "",
    "User_Suspension_Date__c": "",

    "Email_Validation_Status__c": true,

    "First_Name__c": "",
    "Last_Name__c": "",
    "Full_Name__c": "",
    "Email__c": "",

    "Organization_Name__c": "",
    "Organization_URL__c": "",
    "Organization_Headquarters__c": "",

    "Branch_Name__c": "",
    "Branch_Path__c": "",
    "Branches_Codes__c": 0,

    "Job_Title__c": "",
    "Employment_Type__c": "",
    "Role_Type__c": "",
    "Employment_Begin_Date__c": "",
    "Direct_Manager__c": "",

    "Backbone_Partner__c": false,
    "Back_Partner_Type__c": "",

    "Gender_Identity__c": "",
    "Race_Identity__c": "",

    "Initiative__c": "",
    "National_Regional_or_Local__c": "",

    // FIX: Add missing fields that exist on Docebo_Users__c
    "Languages__c": "",
    "Time_Zone__c": "",
    "Network_Partnership_Association__c": "",
    "City__c": "",
    "State__c": ""
}

function tidyData(userInfo, userListedInfo) {
    let tmpUserInfo = { ...saveUserInfo }; // Create a new object to avoid shared state
    let newUser = userInfo;
    let additionFields = ('additional_fields' in newUser) ? newUser.additional_fields : [];

    console.log("🔍 Available additional fields:", JSON.stringify(additionFields, null, 2));

    // Remove references to fields that don't exist on Docebo_Users__c
    // tmpUserInfo.Best_Describes_Your_Affiliation__c = null; // Field doesn't exist
    // tmpUserInfo.Network_Partnership_Association__c = null; // Field doesn't exist
    // tmpUserInfo.OwnerId = "005O400000BxnnxIAB"; // Field doesn't exist
    // tmpUserInfo.Partner_with_a_Member_of_StriveTogether__c = null; // Field doesn't exist
    // tmpUserInfo.StriveTogether_Network_Member__c = null; // Field doesn't exist
    // tmpUserInfo.User__c = null; // Field doesn't exist
    // tmpUserInfo.Who__c = null; // Field doesn't exist

    tmpUserInfo.Back_Partner_Type__c = getAdditionalData(additionFields, "16");
    tmpUserInfo.Backbone_Partner__c = getAdditionalData(additionFields, "15") === 'Yes';
    tmpUserInfo.Branch_Name__c = Array.isArray(newUser.branches) && newUser.branches.length > 0 ? newUser.branches[0].name : "";
    tmpUserInfo.Branch_Path__c = Array.isArray(newUser.branches) && newUser.branches.length > 0 ? newUser.branches[0].path : "";
    // Convert branch codes to numeric value or 0 if not a valid number
    const branchCodes = Array.isArray(newUser.branches) && newUser.branches.length > 0 ? newUser.branches[0].codes : "";
    tmpUserInfo.Branches_Codes__c = branchCodes && !isNaN(parseFloat(branchCodes)) ? parseFloat(branchCodes) : 0;
    tmpUserInfo.Direct_Manager__c = newUser.user_data.manager_username;
    tmpUserInfo.Email__c = newUser.user_data.email;
    tmpUserInfo.Email_Validation_Status__c = newUser.user_data.email_validation_status === '1';
    tmpUserInfo.Employment_Begin_Date__c = getAdditionalData(additionFields, "17");
    tmpUserInfo.Employment_Type__c = getAdditionalData(additionFields, "10");
    tmpUserInfo.First_Name__c = newUser.user_data.first_name;
    tmpUserInfo.Full_Name__c = `${newUser.user_data.first_name} ${newUser.user_data.last_name}`;
    tmpUserInfo.Gender_Identity__c = getAdditionalData(additionFields, "13");
    tmpUserInfo.Job_Title__c = getAdditionalData(additionFields, "8");
    tmpUserInfo.Initiative__c = getAdditionalData(additionFields, "20");
    tmpUserInfo.Last_Name__c = newUser.user_data.last_name;
    // tmpUserInfo.Level__c = newUser.user_data.level; // Field doesn't exist (use User_Level__c instead)
    tmpUserInfo.National_Regional_or_Local__c = getAdditionalData(additionFields, "21");
    tmpUserInfo.Organization_Name__c = getAdditionalData(additionFields, "14");
    tmpUserInfo.Organization_Headquarters__c = getAdditionalData(additionFields, "22");
    tmpUserInfo.Organization_URL__c = getAdditionalData(additionFields, "23") || ""; // Map from additional field if available
    tmpUserInfo.Race_Identity__c = getAdditionalData(additionFields, "12");
    tmpUserInfo.Role_Type__c = getAdditionalData(additionFields, "9");
    // FIX: Handle creation date properly with fallback to current date
    if (newUser.fired_at && newUser.fired_at !== "") {
        tmpUserInfo.User_Creation_Date__c = new Date(newUser.fired_at.replace(' ', 'T')).toISOString();
    } else {
        // Fallback to current date if fired_at is missing
        tmpUserInfo.User_Creation_Date__c = new Date().toISOString();
        console.log(`⚠️ No fired_at date provided for user ${tmpUserInfo.User_Unique_Id__c}, using current date`);
    }

    if (newUser.expiration_date && newUser.expiration_date !== "") {
        tmpUserInfo.User_Expiration_Date__c = new Date(newUser.expiration_date.replace(' ', 'T')).toISOString();
    } else {
        // Use creation date as expiration date if not provided
        tmpUserInfo.User_Expiration_Date__c = tmpUserInfo.User_Creation_Date__c;
    }
    tmpUserInfo.User_Level__c = newUser.user_data.level;
    tmpUserInfo.User_Unique_Id__c = parseInt(newUser.user_data.user_id, 10); // Ensure user_id is mapped correctly
    // tmpUserInfo.Username__c = newUser.user_data.username; // Field doesn't exist on Docebo_Users__c
    tmpUserInfo.Deactivated__c = newUser.user_data.valid === '1';
    tmpUserInfo.User_Last_Access_Date__c = (userListedInfo && userListedInfo.last_access_date) ? new Date(userListedInfo.last_access_date).toISOString() : null;
    tmpUserInfo.User_Suspension_Date__c = (newUser.user_data.valid === '0') ? new Date().toISOString() : null;

    // FIX: Add missing field mappings that exist on Docebo_Users__c
    tmpUserInfo.Languages__c = newUser.user_data.language || ""; // FIX: Map from user_data.language
    tmpUserInfo.Time_Zone__c = newUser.user_data.timezone || ""; // FIX: Map from user_data.timezone
    tmpUserInfo.Network_Partnership_Association__c = getAdditionalData(additionFields, "14") || ""; // FIX: Map from additional field
    tmpUserInfo.City__c = getAdditionalData(additionFields, "24") || ""; // FIX: Map from mailing city
    tmpUserInfo.State__c = getStateLabel(additionFields, "25") || ""; // FIX: Map from mailing state

    // All additional fields removed - they don't exist on Docebo_Users__c
    // These fields are only used for Lead creation, not Docebo_Users__c
    // tmpUserInfo.Languages__c = newUser.user_data.language || ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.Phone__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.Fax__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.Salutation__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.MailingStreet__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.MailingCity__c = getAdditionalData(additionFields, "24") || ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.MailingState__c = getStateLabel(additionFields, "25") || ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.MailingPostalCode__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.MailingCountry__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.Website__c = getAdditionalData(additionFields, "23") || ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.Industry__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.AnnualRevenue__c = 0; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.NumberOfEmployees__c = 0; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.Rating__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.Position_Role__c = tmpUserInfo.Role_Type__c || ""; // Field doesn't exist on Docebo_Users__c

    // Enhanced logging for processed data
    console.log(`🔧 PROCESSED USER DATA for User ${tmpUserInfo.User_Unique_Id__c}:`);
    console.log("Processed data from tidyData function:", JSON.stringify(tmpUserInfo, null, 2));

    // Log specific field mappings for debugging - only fields that exist on Docebo_Users__c
    console.log("📊 KEY FIELD MAPPINGS:");
    console.log(`   Organization_Name__c: "${tmpUserInfo.Organization_Name__c}"`);
    console.log(`   Job_Title__c: "${tmpUserInfo.Job_Title__c}"`);
    console.log(`   Role_Type__c: "${tmpUserInfo.Role_Type__c}"`);
    console.log(`   Race_Identity__c: "${tmpUserInfo.Race_Identity__c}"`);
    console.log(`   Gender_Identity__c: "${tmpUserInfo.Gender_Identity__c}"`);
    console.log(`   Initiative__c: "${tmpUserInfo.Initiative__c}"`);
    console.log(`   National_Regional_or_Local__c: "${tmpUserInfo.National_Regional_or_Local__c}"`);

    // FIX: Log the newly added field mappings
    console.log(`   Languages__c: "${tmpUserInfo.Languages__c}"`);
    console.log(`   Time_Zone__c: "${tmpUserInfo.Time_Zone__c}"`);
    console.log(`   Network_Partnership_Association__c: "${tmpUserInfo.Network_Partnership_Association__c}"`);
    console.log(`   City__c: "${tmpUserInfo.City__c}"`);
    console.log(`   State__c: "${tmpUserInfo.State__c}"`);

    return tmpUserInfo;
}

const getAdditionalData = (additionalArr, fieldId) => {
    let optionLabel = "";
    additionalArr.forEach(element => {
        if (element.id == fieldId) {
            if (element.enabled == true) {
                if ('options' in element) {
                    element.options.forEach(elOpt => {
                        if (elOpt.id == element.value) {
                            optionLabel = elOpt.label;
                        }
                    })
                } else {
                    optionLabel = element.value;
                }
            }
        }
    });
    return optionLabel;
}

// Helper function specifically for state dropdown (ID 25) - needed for Lead creation
const getStateLabel = (additionalArr, fieldId) => {
    let stateLabel = "";
    additionalArr.forEach(element => {
        if (element.id == fieldId) {
            if (element.enabled == true && element.value && element.value !== "null") {
                if ('options' in element) {
                    element.options.forEach(elOpt => {
                        if (elOpt.id == element.value) {
                            stateLabel = elOpt.label;
                        }
                    })
                }
            }
        }
    });
    return stateLabel;
}

// Helper function to try alternative Lead association when direct linking fails
async function tryAlternativeLeadAssociation(conn, doceboUserId, email) {
    if (!email) {
        console.log(`⚠️ No email provided for alternative Lead association`);
        return;
    }

    try {
        console.log(`🔍 Trying alternative Lead association for email: ${email}`);

        // FIX: First check if there are already successful associations for this email
        // This helps us find Leads that pass the lookup filter
        const existingAssociations = await conn.sobject("Docebo_Users__c")
            .find({
                Email__c: email,
                Lead__c: { $ne: null }
            })
            .limit(1)
            .execute();

        if (existingAssociations.length > 0) {
            const existingLeadId = existingAssociations[0].Lead__c;
            console.log(`🔍 Found existing successful association with Lead: ${existingLeadId}`);

            // Try to use the same Lead that worked before
            try {
                const linkResult = await conn.sobject("Docebo_Users__c").update({
                    Id: doceboUserId,
                    Lead__c: existingLeadId
                });

                if (linkResult.success) {
                    console.log(`✅ Reused successful Lead: Docebo_Users__c ${doceboUserId} linked to Lead ${existingLeadId}`);
                    return true;
                } else {
                    console.log(`⚠️ Reusing existing Lead failed:`, linkResult.errors);
                }
            } catch (reuseError) {
                console.log(`⚠️ Error reusing existing Lead: ${reuseError.message}`);
            }
        }

        // FIX: Find Leads with matching email, prioritizing older ones that are more likely to pass filters
        const leads = await conn.sobject("Lead")
            .find({
                Email: email,
                IsConverted: false, // Only unconverted leads
                Created_by_Docebo_API__c: true // Only Docebo-created leads
            })
            .sort({ CreatedDate: 1 }) // Oldest first - more likely to pass time-based filters
            .limit(5)
            .execute();

        if (leads.length > 0) {
            console.log(`📋 Found ${leads.length} potential Lead(s) for association`);

            // FIX: If multiple leads exist, this indicates duplicates - we should clean them up
            if (leads.length > 1) {
                console.log(`⚠️ Multiple Leads detected for ${email} - this indicates duplicate creation`);
                console.log(`   Lead IDs: ${leads.map(l => l.Id).join(', ')}`);
                console.log(`   Recommendation: Implement better duplicate prevention`);
            }

            // Try each Lead until one works, starting with the oldest
            for (const lead of leads) {
                try {
                    console.log(`🧪 Testing Lead ${lead.Id} (Created: ${lead.CreatedDate})`);

                    const linkResult = await conn.sobject("Docebo_Users__c").update({
                        Id: doceboUserId,
                        Lead__c: lead.Id
                    });

                    if (linkResult.success) {
                        console.log(`✅ Alternative association successful: Docebo_Users__c ${doceboUserId} linked to Lead ${lead.Id}`);
                        await setLeadBackReference(conn, lead.Id, doceboUserId);

                        // FIX: Clean up duplicate Leads if association was successful
                        if (leads.length > 1) {
                            console.log(`🗑️ Cleaning up ${leads.length - 1} duplicate Lead(s)...`);
                            for (let i = 1; i < leads.length; i++) {
                                try {
                                    await conn.sobject("Lead").delete(leads[i].Id);
                                    console.log(`   ✅ Deleted duplicate Lead: ${leads[i].Id}`);
                                } catch (deleteError) {
                                    console.log(`   ⚠️ Could not delete duplicate Lead ${leads[i].Id}: ${deleteError.message}`);
                                }
                            }
                        }

                        return true;
                    } else {
                        console.log(`⚠️ Alternative Lead ${lead.Id} failed:`, linkResult.errors);
                    }
                } catch (altError) {
                    console.log(`⚠️ Alternative Lead ${lead.Id} error: ${altError.message}`);

                    // FIX: If this is a lookup filter error, provide more specific guidance
                    if (altError.errorCode === 'FIELD_FILTER_VALIDATION_EXCEPTION') {
                        console.log(`   🚨 Lookup filter rejection - Lead ${lead.Id} doesn't meet filter criteria`);
                        console.log(`   💡 Consider contacting Salesforce admin to review Lead__c lookup filter`);
                    }
                }
            }

            console.log(`❌ All alternative Lead associations failed for ${email}`);
            console.log(`💡 Recommendation: Use Contact association instead of Lead for this user`);
        } else {
            console.log(`⚠️ No unconverted Docebo-created Leads found with email: ${email}`);
        }
    } catch (error) {
        console.log(`❌ Error in alternative Lead association: ${error.message}`);
    }

    return false;
}

// WORKAROUND: Contact association when Lead association is blocked by lookup filter
async function tryContactAssociationWorkaround(conn, doceboUserId, tmpUserInfo, userInfo) {
    try {
        console.log(`🔄 WORKAROUND: Attempting Contact association for Docebo_Users__c ${doceboUserId}`);

        // FIX: Check if there's already a Contact with this email (broader search to prevent duplicates)
        const existingContacts = await conn.sobject("Contact")
            .find({
                Email: tmpUserInfo.Email__c
                // REMOVED: Active_Portal_User__c filter to prevent duplicate creation
                // We'll update any existing Contact to be an active portal user
            })
            .limit(1)
            .execute();

        let contactId = null;

        if (existingContacts.length > 0) {
            contactId = existingContacts[0].Id;
            console.log(`✅ Found existing Contact: ${contactId}`);

            // FIX: Update the existing Contact with latest info AND ensure it's marked as active portal user
            try {
                const contactUpdateData = {
                    Id: contactId,
                    FirstName: tmpUserInfo.First_Name__c,  // FIX: Use the VALUE from Docebo_Users__c field
                    LastName: tmpUserInfo.Last_Name__c,    // FIX: Use the VALUE from Docebo_Users__c field
                    Email: tmpUserInfo.Email__c,           // FIX: Use the VALUE from Docebo_Users__c field
                    Title: tmpUserInfo.Job_Title__c,       // FIX: Use the VALUE from Docebo_Users__c field
                    // FIX: Use ONLY approved Contact fields - remove non-existent custom fields
                    Active_Portal_User__c: true,
                    Created_by_Docebo_API__c: true,
                    LeadSource: "Docebo Platform",
                    // Add other approved Contact fields
                    Annual_Revenue__c: tmpUserInfo.Annual_Revenue__c || 0,
                    Company__c: tmpUserInfo.Organization_Name__c || "",
                    Contact_Type__c: tmpUserInfo.Contact_Type__c || "Other",
                    Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
                    Gateway__c: "Docebo API",
                    GenderIdentity: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c),
                    Industry__c: tmpUserInfo.Industry__c || "",
                    Initiative__c: tmpUserInfo.Initiative__c || "",
                    Languages__c: tmpUserInfo.Languages__c || "",
                    Legacy_ID__c: String(tmpUserInfo.User_Unique_Id__c || ""),
                    Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",
                    NumberOfEmployees__c: tmpUserInfo.NumberOfEmployees__c || 0,
                    Position_Role__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
                    Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),
                    Rating__c: tmpUserInfo.Rating__c || "",
                    Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
                    Time_Zone__c: tmpUserInfo.Time_Zone__c || "",
                    Type__c: "Backbone Staff", // FIX: Use valid picklist value
                    Website__c: tmpUserInfo.Organization_URL__c || ""
                };

                const updateResult = await conn.sobject("Contact").update(contactUpdateData);
                if (updateResult.success) {
                    console.log(`✅ Updated existing Contact: ${contactId}`);
                } else {
                    console.log(`⚠️ Could not update Contact:`, updateResult.errors);
                }
            } catch (updateError) {
                console.log(`⚠️ Error updating Contact: ${updateError.message}`);
            }
        } else {
            console.log(`🔄 No existing Contact found, creating new Contact...`);

            // REMOVED: Contact creation logic - we only want Lead and Docebo_Users__c
            console.log(`ℹ️ Contact creation DISABLED - only creating Lead and Docebo_Users__c records`);
            return false; // Skip Contact creation entirely

        }

        return false;
    } catch (error) {
        console.log(`❌ Error in Contact association workaround: ${error.message}`);
        return false;
    }
}

module.exports = {
    createNewUser,
    getTotalUserList,
    saveUsersInBatch,
    tidyData
};