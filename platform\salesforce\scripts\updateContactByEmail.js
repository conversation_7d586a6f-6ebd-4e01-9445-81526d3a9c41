const getConnection = require('../common/getConnection');

(async function main() {
  try {
    // Hardcoded email to find the Contact by
    const email = 'vlad<PERSON><EMAIL>';

    // Hardcoded fields to update (use valid values for your org's picklists)
    const updates = {
      // Common fields
      Title: 'Senior Manager',
      Description: 'Updated by updateContactByEmail.js test script',

      // Identity and role (Contact uses these API names in this codebase)
      GenderIdentity: 'Man', // e.g., 'Man', 'Woman', 'Non-Binary or other gender identity', 'Prefer not to respond'
      Role_Type__c: 'Programs', // Ensure this matches a valid picklist value in your org
      Employment_Type__c: 'Full-Time',
      Race__c: 'White', // e.g., 'White', 'Asian', 'Black or African American', 'Hispanic or Latine'

      // Address & locale
      mailingcity__c: 'Seattle',
      mailingstate__c: 'WA', // Use state/region label your org expects
      Languages__c: 'en',
      Time_Zone__c: 'America/Los_Angeles',

      // Business info
      Company__c: 'Example Org',
      Initiative__c: 'Backbone Initiative',
      Industry__c: 'Not For Profit',
      NumberOfEmployees__c: 100,
      Annual_Revenue__c: 5000000,
      Organization_Employer__c: 'Example Org',
      Rating__c: 'Warm',
      Type__c: 'Backbone Staff',
      Website__c: 'https://www.example.org',
      Network_Partnership_Association__c: 'Example Network',

      // Flags and source
      Active_Portal_User__c: true,
      Inactive_Contact__c: false,
      Created_by_Docebo_API__c: true,
      LeadSource: 'Docebo Platform',
      Gateway__c: 'Docebo API',

      // Other
      Contact_Type__c: 'Other',
      Legacy_ID__c: 'DU-TEST-1234',
      FTE__c: 'Full-Time'
    };

    const conn = await getConnection();
    if (!conn || !conn.accessToken) {
      console.error('❌ Could not get Salesforce connection.');
      process.exit(1);
    }

    console.log(`🔎 Looking up Contact by Email='${email}' ...`);
    const contact = await conn.sobject('Contact').findOne({ Email: email });

    if (!contact) {
      console.log(`⚠️ No Contact found with email: ${email}`);
      process.exit(0);
    }

    const payload = { Id: contact.Id, ...updates };
    console.log('📝 Updating Contact with payload:', payload);

    const res = await conn.sobject('Contact').update(payload);
    if (res.success) {
      console.log(`✅ Contact ${contact.Id} updated successfully.`);
      process.exit(0);
    } else {
      console.error('❌ Update failed:', res.errors);
      process.exit(1);
    }
  } catch (err) {
    console.error('❌ Unexpected error:', err && (err.message || err.toString()));
    process.exit(1);
  }
})();

