#!/usr/bin/env node

/**
 * Cache Docebo Users Data
 * 
 * This script fetches all users from Docebo API and saves them to a JSON file
 * so you can re-run sync operations without hitting the API again
 */

require('dotenv').config();
const fs = require('fs').promises;
const path = require('path');
const doceboService = require("./platform/docebo/services");

const CACHE_FILE = './data/docebo-users-cache.json';

async function cacheDoceboUsers() {
    try {
        console.log('📥 CACHING DOCEBO USERS DATA');
        console.log('=' .repeat(50));
        console.log('This will:');
        console.log('1. Fetch ALL users from Docebo API');
        console.log('2. Get detailed info for each user');
        console.log('3. Save everything to a JSON cache file');
        console.log('4. Allow you to re-run sync without API calls');
        console.log('');
        
        const startTime = Date.now();
        
        // Step 1: Fetch all users from Docebo
        console.log('📋 Fetching user list from Docebo...');
        const allUsers = await doceboService.getTotalUserListedInfo();
        
        if (!allUsers || allUsers.length === 0) {
            console.log('⚠️ No users found in Docebo');
            return false;
        }
        
        console.log(`✅ Found ${allUsers.length} users in Docebo`);
        
        // Step 2: Get detailed info for each user
        console.log('\n📋 Fetching detailed info for each user...');
        const usersWithDetails = [];
        let processedCount = 0;
        
        for (const user of allUsers) {
            try {
                console.log(`   Processing user ${processedCount + 1}/${allUsers.length}: ${user.firstname} ${user.lastname} (ID: ${user.user_id})`);
                
                // Get detailed user info
                const detailedUser = await doceboService.getUserInfo(user.user_id);
                
                // Store both basic and detailed info
                usersWithDetails.push({
                    basicInfo: user,
                    detailedInfo: detailedUser,
                    cachedAt: new Date().toISOString()
                });
                
                processedCount++;
                
                // Progress update every 100 users
                if (processedCount % 100 === 0) {
                    console.log(`   📊 Progress: ${processedCount}/${allUsers.length} users processed`);
                }
                
                // Small delay to avoid overwhelming the API
                await new Promise(resolve => setTimeout(resolve, 100));
                
            } catch (error) {
                console.error(`   ❌ Error processing user ${user.user_id}:`, error.message);
                // Continue with next user
            }
        }
        
        // Step 3: Save to cache file
        console.log('\n💾 Saving users to cache file...');
        
        // Ensure data directory exists
        await fs.mkdir('./data', { recursive: true });
        
        const cacheData = {
            cachedAt: new Date().toISOString(),
            totalUsers: usersWithDetails.length,
            users: usersWithDetails
        };
        
        await fs.writeFile(CACHE_FILE, JSON.stringify(cacheData, null, 2));
        
        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);
        
        console.log('\n🎉 DOCEBO USERS CACHED SUCCESSFULLY!');
        console.log(`📁 Cache file: ${CACHE_FILE}`);
        console.log(`👥 Total users cached: ${usersWithDetails.length}`);
        console.log(`⏱️  Total time: ${duration} seconds`);
        console.log('');
        console.log('✅ You can now run sync operations using the cached data!');
        console.log('   Use: node run-sync-from-cache.js');
        
        return true;
        
    } catch (error) {
        console.error('❌ Failed to cache Docebo users:', error.message);
        console.error('Stack trace:', error.stack);
        return false;
    }
}

// Run the caching
cacheDoceboUsers()
    .then(success => {
        if (success) {
            console.log('\n✅ Caching completed successfully.');
            process.exit(0);
        } else {
            console.log('\n❌ Caching failed.');
            process.exit(1);
        }
    })
    .catch(err => {
        console.error('💥 Caching process failed:', err);
        process.exit(1);
    });
