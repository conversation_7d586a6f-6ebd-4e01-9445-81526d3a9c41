#!/usr/bin/env node

/**
 * Test Small User Sync - 5 Users Only
 *
 * This script tests the user sync process with just 5 users to verify everything works
 */


// Toggle file logging here (default: enabled). Set to false to disable.
const ENABLE_FILE_LOG = true;
if (ENABLE_FILE_LOG && !process.env.SMALL_SYNC_FILE_LOG && !process.env.HISTORICAL_FILE_LOG) {
  process.env.SMALL_SYNC_FILE_LOG = '1';
}

require('dotenv').config();

// Optional file logging for test runs: set SMALL_SYNC_FILE_LOG=1 or HISTORICAL_FILE_LOG=1 to enable
const fs = require('fs');
const path = require('path');
(function setupFileLogger() {
  try {
    if (process.env.SMALL_SYNC_FILE_LOG === '1' || process.env.HISTORICAL_FILE_LOG === '1') {
      const logsDir = path.join(process.cwd(), 'logs');
      if (!fs.existsSync(logsDir)) fs.mkdirSync(logsDir, { recursive: true });
      const ts = new Date().toISOString().replace(/[:.]/g, '-');
      const logPath = path.join(logsDir, `small-sync-${ts}.log`);
      const stream = fs.createWriteStream(logPath, { flags: 'a' });

      const origLog = console.log.bind(console);
      const origWarn = console.warn.bind(console);
      const origError = console.error.bind(console);

      const safe = (v) => {
        if (v instanceof Error) return `${v.name}: ${v.message}`;
        if (typeof v === 'string') return v;
        try { return JSON.stringify(v); } catch { return String(v); }
      };
      const stamp = (lvl, args) => `[${new Date().toISOString()}] [${lvl}] ${args.map(safe).join(' ')}\n`;

      console.log = (...args) => { try { stream.write(stamp('INFO', args)); } catch {} origLog(...args); };
      console.warn = (...args) => { try { stream.write(stamp('WARN', args)); } catch {} origWarn(...args); };
      console.error = (...args) => { try { stream.write(stamp('ERROR', args)); } catch {} origError(...args); };

      origLog(`📝 Small-sync file logging enabled: ${logPath}`);
    }
  } catch (e) {
    console.warn('⚠️ Failed to initialize small-sync file logger:', e && (e.message || e.toString()));
  }
})();

const { updateHistoricalData } = require('./platform/salesforce/users/historicalDataUpdate');

async function testSmallUserSync() {
    try {
        console.log('🧪 TESTING SMALL USER SYNC - 5 USERS ONLY');
        console.log('=' .repeat(60));
        console.log('This process will:');
        console.log('1. Fetch ONLY 5 users from Docebo API');
        console.log('2. For each user, check if they exist in Salesforce');
        console.log('3. Create Lead if user doesn\'t exist');
        console.log('4. Update Contact if user exists as Contact');
        console.log('5. Update Lead if user exists as Lead');
        console.log('6. Create/update Docebo_Users__c record');
        console.log('');
        console.log('🎯 This is a TEST RUN with limited users to verify the process works');
        console.log('');

        const startTime = Date.now();

        // Call the historical data update function with a limit of 5 users
        console.log('🚀 Starting small user sync test...');
        await updateHistoricalData(100); // Pass limit of 5 users

        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);

        console.log('');
        console.log('🎉 SMALL USER SYNC TEST COMPLETED SUCCESSFULLY!');
        console.log(`⏱️  Total time: ${duration} seconds`);
        console.log('');
        console.log('✅ Test completed with 5 users:');
        console.log('   • Existing Contacts updated with comprehensive fields');
        console.log('   • Existing Leads updated with comprehensive fields');
        console.log('   • New Leads created for users not in Salesforce');
        console.log('   • All Docebo_Users__c records updated/created');
        console.log('');
        console.log('🎯 If this test looks good, you can run the full sync!');

    } catch (error) {
        console.error('❌ Small user sync test failed:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Run the test
testSmallUserSync().catch(console.error);
