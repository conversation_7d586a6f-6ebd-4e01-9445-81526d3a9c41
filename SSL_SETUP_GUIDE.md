# 🔒 SSL Setup Guide for Docebo-Salesforce Integration

## 📋 Overview

Your `app.js` has been updated to support both HTTP and HTTPS with SSL certificates. This guide shows you how to set up SSL certificates and deploy your application.

## 🎯 What Changed in app.js

### New Features:
- ✅ **Dual Server Support**: HTTP (port 5000) + HTTPS (port 443)
- ✅ **Automatic SSL Detection**: Checks for certificates in production
- ✅ **Health Check Endpoint**: `/health` for monitoring
- ✅ **Graceful Shutdown**: Proper process handling
- ✅ **Environment-based Configuration**: Different behavior for dev/prod

### Server Behavior:
- **Development**: HTTP only on port 5000
- **Production**: HTTP (port 5000) + HTTPS (port 443) if certificates exist

---

## 🔧 SSL Certificate Setup Options

### Option 1: Let's Encrypt (Free SSL - Recommended)

**Step 1: Install Certbot**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y certbot

# CentOS/RHEL
sudo yum install -y certbot
```

**Step 2: Get SSL Certificate**
```bash
# Get SSL certificate for communityreport.org
sudo certbot certonly --standalone -d communityreport.org

# Certificate files will be created at:
# /etc/letsencrypt/live/communityreport.org/privkey.pem (private key)
# /etc/letsencrypt/live/communityreport.org/fullchain.pem (certificate)
```

**Step 3: Copy Certificates to Expected Locations**
```bash
# Create SSL directories
sudo mkdir -p /etc/ssl/private /etc/ssl/certs

# Copy certificates (run after getting Let's Encrypt certs)
sudo cp /etc/letsencrypt/live/communityreport.org/privkey.pem /etc/ssl/private/server.key
sudo cp /etc/letsencrypt/live/communityreport.org/fullchain.pem /etc/ssl/certs/server.crt

# Set proper permissions
sudo chmod 600 /etc/ssl/private/server.key
sudo chmod 644 /etc/ssl/certs/server.crt
sudo chown root:root /etc/ssl/private/server.key /etc/ssl/certs/server.crt
```

**Step 4: Set Up Auto-Renewal**
```bash
# Test renewal
sudo certbot renew --dry-run

# Add to crontab for auto-renewal
sudo crontab -e
# Add this line to renew certificates monthly:
0 2 1 * * /usr/bin/certbot renew --quiet && /bin/systemctl restart your-app-service
```

### Option 2: Self-Signed Certificate (Development/Testing)

**Create Self-Signed Certificate:**
```bash
# Create SSL directories
sudo mkdir -p /etc/ssl/private /etc/ssl/certs

# Generate private key
sudo openssl genrsa -out /etc/ssl/private/server.key 2048

# Generate certificate (valid for 365 days)
sudo openssl req -new -x509 -key /etc/ssl/private/server.key -out /etc/ssl/certs/server.crt -days 365

# You'll be prompted for certificate details:
# Country Name: US
# State: Your State
# City: Your City
# Organization: Your Organization
# Organizational Unit: IT Department
# Common Name: your-domain.com (IMPORTANT: Use your actual domain)
# Email: <EMAIL>

# Set permissions
sudo chmod 600 /etc/ssl/private/server.key
sudo chmod 644 /etc/ssl/certs/server.crt
```

### Option 3: Commercial SSL Certificate

If you have a commercial SSL certificate:

```bash
# Copy your certificate files to the expected locations
sudo cp your-private-key.key /etc/ssl/private/server.key
sudo cp your-certificate.crt /etc/ssl/certs/server.crt

# Set permissions
sudo chmod 600 /etc/ssl/private/server.key
sudo chmod 644 /etc/ssl/certs/server.crt
sudo chown root:root /etc/ssl/private/server.key /etc/ssl/certs/server.crt
```

---

## ⚙️ Environment Configuration

### Update Your .env File

Your `.env` file now includes SSL configuration:

```env
# Production Configuration
NODE_ENV=production
PORT=5000
HTTPS_PORT=443

# SSL Certificate Configuration
SSL_KEY_PATH=/etc/ssl/private/server.key
SSL_CERT_PATH=/etc/ssl/certs/server.crt

# Webhook URLs - Update with your actual domain
DOCEBO_WEBHOOK_URL="https://your-actual-domain.com"
SALESFORCE_WEBHOOK_URL="https://your-actual-domain.com"
HUBSPOT_WEBHOOK_URL="https://your-actual-domain.com"
```

### Custom SSL Certificate Paths

If your certificates are in different locations:

```env
# Custom certificate paths
SSL_KEY_PATH=/path/to/your/private.key
SSL_CERT_PATH=/path/to/your/certificate.crt
```

---

## 🚀 Deployment Steps

### Step 1: Prepare Your Server

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2
sudo npm install -g pm2

# Create application user
sudo adduser --disabled-password --gecos "" docebo-app
sudo usermod -aG sudo docebo-app
```

### Step 2: Deploy Application

```bash
# Switch to application user
sudo su - docebo-app

# Clone and setup application
git clone <your-repo> docebo-salesforce
cd docebo-salesforce
npm install --production

# Configure environment
cp .env.example .env
nano .env  # Update with your actual values
```

### Step 3: Set Up SSL Certificates

Choose one of the SSL options above and set up your certificates.

### Step 4: Configure Firewall

```bash
# Enable firewall
sudo ufw enable

# Allow SSH
sudo ufw allow 22/tcp

# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Check status
sudo ufw status
```

### Step 5: Start Application

```bash
# Start with PM2
pm2 start app.js --name "docebo-salesforce"

# Configure PM2 to start on boot
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u docebo-app --hp /home/<USER>

# Save PM2 configuration
pm2 save
```

---

## 🔍 Testing Your Setup

### Test HTTP Server
```bash
curl http://localhost:5000/health
# Expected response: {"status":"healthy","timestamp":"...","environment":"production"}
```

### Test HTTPS Server
```bash
curl https://your-domain.com/health
# Expected response: {"status":"healthy","timestamp":"...","environment":"production"}
```

### Test Webhook Endpoints
```bash
# Test user management webhook
curl -X POST https://your-domain.com/webhook/docebo/user/manage \
  -H "Content-Type: application/json" \
  -d '{"message_id":"test","event":"user.created","payloads":[]}'

# Expected response: {"status":"success","message":"Webhook received, processing in background"}
```

---

## 📊 Monitoring & Logs

### Check Application Status
```bash
# PM2 status
pm2 status

# View logs
pm2 logs docebo-salesforce

# Monitor resources
pm2 monit
```

### Check SSL Certificate Status
```bash
# Check certificate expiration
openssl x509 -in /etc/ssl/certs/server.crt -text -noout | grep "Not After"

# Test SSL connection
openssl s_client -connect your-domain.com:443 -servername your-domain.com
```

---

## 🔧 Troubleshooting

### Common Issues

**1. Permission Denied on Port 443**
```bash
# Error: EACCES: permission denied to bind to port 443
# Solution: Run with sudo or use authbind

# Option 1: Use authbind
sudo apt install authbind
sudo touch /etc/authbind/byport/443
sudo chmod 500 /etc/authbind/byport/443
sudo chown docebo-app /etc/authbind/byport/443

# Start with authbind
authbind --deep pm2 start app.js --name "docebo-salesforce"

# Option 2: Use different HTTPS port
# In .env file: HTTPS_PORT=8443
# Then allow port 8443 in firewall: sudo ufw allow 8443/tcp
```

**2. SSL Certificate Not Found**
```bash
# Check if certificates exist
ls -la /etc/ssl/private/server.key
ls -la /etc/ssl/certs/server.crt

# Check permissions
sudo chmod 600 /etc/ssl/private/server.key
sudo chmod 644 /etc/ssl/certs/server.crt
```

**3. Domain Not Resolving**
```bash
# Check DNS resolution
nslookup your-domain.com
dig your-domain.com

# Make sure your domain points to your server's IP address
```

---

## 🎯 Next Steps

1. **Update Docebo Webhooks**: Configure webhooks in Docebo admin to use your HTTPS URLs
2. **Monitor Logs**: Watch PM2 logs for any SSL-related errors
3. **Set Up Monitoring**: Consider adding uptime monitoring for your endpoints
4. **Backup Certificates**: Regularly backup your SSL certificates
5. **Plan Renewal**: Set up automatic certificate renewal (especially for Let's Encrypt)

Your application now supports both HTTP and HTTPS with proper SSL certificate handling!
