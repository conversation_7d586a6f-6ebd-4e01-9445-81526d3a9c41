#!/usr/bin/env node

require('dotenv').config();
const axios = require('axios');
const getSFToken = require('./platform/salesforce/common/getAccessToken');

function usage() {
  console.log('Usage: node verify-sf-records.js <LEAD_ID> <DOCEBO_USER_ID>');
  console.log('Example: node verify-sf-records.js 00Qxxxxxxxxxxxx a2Exxxxxxxxxxxx');
}

async function fetchRecord(instanceUrl, accessToken, sobject, id) {
  try {
    const res = await axios.get(`${instanceUrl}/services/data/v60.0/sobjects/${sobject}/${id}`, {
      headers: { Authorization: `Bearer ${accessToken}` }
    });
    return { ok: true, data: res.data };
  } catch (e) {
    return { ok: false, status: e.response?.status, data: e.response?.data, message: e.message };
  }
}

async function main() {
  const leadId = process.argv[2];
  const duId = process.argv[3];
  if (!leadId || !duId) {
    usage();
    process.exit(1);
  }

  console.log('🔐 Getting Salesforce token...');
  const tokenRes = await getSFToken();
  if (tokenRes.status !== 200) {
    console.error('❌ Failed to get token:', tokenRes.message || tokenRes);
    process.exit(1);
  }
  const { accessToken, instanceUrl } = tokenRes.data;
  console.log('✅ Token OK. Instance:', instanceUrl);

  console.log(`\n🔎 Fetching Lead ${leadId} ...`);
  const lead = await fetchRecord(instanceUrl, accessToken, 'Lead', leadId);
  if (!lead.ok) {
    console.error('❌ Lead fetch failed:', lead.status, lead.data || lead.message);
  } else {
    // Select a subset of fields to display
    const l = lead.data;
    const show = {
      Id: l.Id,
      Name: l.Name,
      FirstName: l.FirstName,
      LastName: l.LastName,
      Company: l.Company,
      Email: l.Email,
      Title: l.Title,
      Website: l.Website,
      Status: l.Status,
      LeadSource: l.LeadSource,
      Contact_Type__c: l.Contact_Type__c,
      Type__c: l.Type__c,
      Docebo_User__c: l.Docebo_User__c,
      Created_by_Docebo_API__c: l.Created_by_Docebo_API__c
    };
    console.log('✅ Lead fields:');
    console.log(JSON.stringify(show, null, 2));
  }

  console.log(`\n🔎 Fetching Docebo_Users__c ${duId} ...`);
  const du = await fetchRecord(instanceUrl, accessToken, 'Docebo_Users__c', duId);
  if (!du.ok) {
    console.error('❌ Docebo_Users__c fetch failed:', du.status, du.data || du.message);
  } else {
    const d = du.data;
    const show = {
      Id: d.Id,
      User_Unique_Id__c: d.User_Unique_Id__c,
      Full_Name__c: d.Full_Name__c,
      First_Name__c: d.First_Name__c,
      Last_Name__c: d.Last_Name__c,
      Email__c: d.Email__c,
      Job_Title__c: d.Job_Title__c,
      Organization_Name__c: d.Organization_Name__c,
      Organization_URL__c: d.Organization_URL__c,
      Languages__c: d.Languages__c,
      Time_Zone__c: d.Time_Zone__c,
      City__c: d.City__c,
      State__c: d.State__c,
      Role_Type__c: d.Role_Type__c,
      Race_Identity__c: d.Race_Identity__c,
      Gender_Identity__c: d.Gender_Identity__c,
      Lead__c: d.Lead__c,
      Contact__c: d.Contact__c,
      User_Creation_Date__c: d.User_Creation_Date__c,
      User_Last_Access_Date__c: d.User_Last_Access_Date__c
    };
    console.log('✅ Docebo_Users__c fields:');
    console.log(JSON.stringify(show, null, 2));
  }
}

main().catch(e => {
  console.error('💥 Unexpected error:', e.message);
  process.exit(1);
});

