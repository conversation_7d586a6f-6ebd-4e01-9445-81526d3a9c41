#!/usr/bin/env node

/**
 * Simple 5 User Test
 *
 * This script tests with exactly 5 users by fetching them directly
 */

require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const doceboService = require("./platform/docebo/services");
const { tidyData } = require("./platform/salesforce/users/createUser");

async function simple5UserTest() {
    try {
        console.log('🧪 SIMPLE 5 USER TEST');
        console.log('=' .repeat(40));

        // Step 1: Get Salesforce connection
        console.log('🔐 Getting Salesforce connection...');
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return false;
        }
        console.log('✅ Salesforce connection established');

        // Step 2: Get first 5 users from Docebo
        console.log('📥 Fetching first 5 users from Docebo...');
        const allUsers = await doceboService.getTotalUserListedInfo();

        if (!allUsers || allUsers.length === 0) {
            console.log('⚠️ No users found in Docebo');
            return false;
        }

        // Take only first 5 users
        const first5Users = allUsers.slice(0, 5);
        console.log(`✅ Got ${first5Users.length} users for testing`);

        // Step 3: Process each user
        let successCount = 0;
        let errorCount = 0;

        for (let i = 0; i < first5Users.length; i++) {
            const user = first5Users[i];
            console.log(`\n👤 Processing user ${i + 1}/5: ${user.firstname} ${user.lastname} (ID: ${user.user_id})`);

            try {
                // Get detailed user info
                const detailedUser = await doceboService.getUserInfo(user.user_id);
                if (!detailedUser || detailedUser.status !== 200) {
                    console.log(`   ⚠️ Failed to get detailed info for user ${user.user_id}`);
                    continue;
                }
                console.log(`   📋 Got detailed info for user ${user.user_id}`);

                // Process the user data (unwrap .data)
                const processedData = tidyData(detailedUser.data, user);
                console.log(`   🔧 Processed user data:`, {
                    name: processedData.Full_Name__c,
                    email: processedData.Email__c,
                    jobTitle: processedData.Job_Title__c,
                    organization: processedData.Organization_Name__c
                });

                // Check if user exists in Salesforce
                const email = processedData.Email__c;
                if (!email) {
                    console.log(`   ⚠️ No email for user ${user.user_id}, skipping`);
                    continue;
                }

                // Search for existing Contact
                let contact = null;
                try {
                    const contactRes = await conn.sobject('Contact').find({ Email: email }).limit(1).execute();
                    contact = (contactRes && contactRes.length > 0) ? contactRes[0] : null;
                } catch (e) {
                    console.log(`   ℹ️ Contact lookup failed: ${e.message || e}`);
                }

                // Search for existing Lead (may be restricted by permissions)
                let lead = null;
                try {
                    const leadRes = await conn.sobject('Lead').find({ Email: email, IsConverted: false }).limit(1).execute();
                    lead = (leadRes && leadRes.length > 0) ? leadRes[0] : null;
                } catch (e) {
                    console.log(`   ℹ️ Lead lookup skipped or failed (likely permission restriction): ${e.message || e}`);
                }

                if (contact) {
                    console.log(`   ✅ Found existing Contact: ${contact.Id}`);
                    // Minimal, safe Contact update (no Account creation)
                    try {
                        const upd = await conn.sobject('Contact').update({
                            Id: contact.Id,
                            LastName: (processedData.Last_Name__c && processedData.Last_Name__c.trim() !== "") ? processedData.Last_Name__c : 'Unknown',
                            FirstName: processedData.First_Name__c || null,
                            Email: processedData.Email__c || null,
                            Title: processedData.Job_Title__c || '',
                            Active_Portal_User__c: !!processedData.Email_Validation_Status__c,
                            Created_by_Docebo_API__c: true,
                            LeadSource: 'Docebo Platform'
                        });
                        if (upd.success) {
                            console.log(`   ✏️ Contact updated: ${contact.Id}`);
                        } else {
                            console.log(`   ⚠️ Contact update failed:`, upd.errors);
                        }
                    } catch (e) {
                        console.log(`   ❌ Contact update error: ${e.message || e}`);
                    }

                    // Ensure Docebo_Users__c exists/updated
                    try {
                        const existingDU = await conn.sobject('Docebo_Users__c').findOne({ User_Unique_Id__c: processedData.User_Unique_Id__c });
                        if (existingDU) {
                            const duUpd = await conn.sobject('Docebo_Users__c').update({ ...processedData, Id: existingDU.Id });
                            console.log(duUpd.success ? `   ✅ Docebo_Users__c updated: ${existingDU.Id}` : `   ⚠️ Docebo_Users__c update failed: ${JSON.stringify(duUpd.errors)}`);
                        } else {
                            const duCreate = await conn.sobject('Docebo_Users__c').create(processedData);
                            console.log(duCreate.success ? `   ✅ Docebo_Users__c created: ${duCreate.id}` : `   ⚠️ Docebo_Users__c creation failed: ${JSON.stringify(duCreate.errors)}`);
                        }
                    } catch (e) {
                        console.log(`   ❌ Docebo_Users__c ensure error: ${e.message || e}`);
                    }

                        // Link Docebo_Users__c -> Contact (and set Email__c to satisfy lookup filters)
                        try {
                            const duRec = await conn.sobject('Docebo_Users__c').findOne({ User_Unique_Id__c: processedData.User_Unique_Id__c });
                            if (duRec) {
                                const link = await conn.sobject('Docebo_Users__c').update({ Id: duRec.Id, Contact__c: contact.Id, Email__c: email });
                                console.log(link.success ? `   Linked DU ${duRec.Id} -> Contact ${contact.Id}` : `   DU->Contact link failed: ${JSON.stringify(link.errors)}`);
                            }
                        } catch (e) {
                            console.log(`   DU->Contact link error: ${e.message || e}`);
                        }

                } else if (lead) {
                    console.log(`   ✅ Found existing Lead: ${lead.Id}`);
                    // Minimal, safe Lead update
                    try {
                        const upd = await conn.sobject('Lead').update({
                            Id: lead.Id,
                            LastName: (processedData.Last_Name__c && processedData.Last_Name__c.trim() !== "") ? processedData.Last_Name__c : 'Unknown',
                            FirstName: processedData.First_Name__c || null,
                            Email: processedData.Email__c || null,
                            Company: processedData.Organization_Name__c || '-',
                            Title: processedData.Job_Title__c || '',
                            Website: processedData.Organization_URL__c || '',
                            Status: 'Open - Not Contacted',
                            Created_by_Docebo_API__c: true,
                            LeadSource: 'Docebo Platform'
                        });
                        if (upd.success) {
                            console.log(`   ✏️ Lead updated: ${lead.Id}`);
                        } else {
                            console.log(`   ⚠️ Lead update failed:`, upd.errors);
                        }
                    } catch (e) {
                        console.log(`   ❌ Lead update error (likely permission restriction): ${e.message || e}`);
                    }

                    // Ensure Docebo_Users__c exists/updated
                    try {
                        const existingDU = await conn.sobject('Docebo_Users__c').findOne({ User_Unique_Id__c: processedData.User_Unique_Id__c });
                        if (existingDU) {
                            const duUpd = await conn.sobject('Docebo_Users__c').update({ ...processedData, Id: existingDU.Id });
                            console.log(duUpd.success ? `   ✅ Docebo_Users__c updated: ${existingDU.Id}` : `   ⚠️ Docebo_Users__c update failed: ${JSON.stringify(duUpd.errors)}`);
                        } else {
                            const duCreate = await conn.sobject('Docebo_Users__c').create(processedData);
                            console.log(duCreate.success ? `   ✅ Docebo_Users__c created: ${duCreate.id}` : `   ⚠️ Docebo_Users__c creation failed: ${JSON.stringify(duCreate.errors)}`);
                        }
                    } catch (e) {
                        console.log(`   ❌ Docebo_Users__c ensure error: ${e.message || e}`);
                    }

                        // Link Docebo_Users__c -> Lead and Lead -> DU
                        try {
                            const duRec = await conn.sobject('Docebo_Users__c').findOne({ User_Unique_Id__c: processedData.User_Unique_Id__c });
                            if (duRec) {
                                const link = await conn.sobject('Docebo_Users__c').update({ Id: duRec.Id, Lead__c: lead.Id, Email__c: email });
                                console.log(link.success ? `   Linked DU ${duRec.Id} -> Lead ${lead.Id}` : `   DU->Lead link failed: ${JSON.stringify(link.errors)}`);
                                try {
                                    const back = await conn.sobject('Lead').update({ Id: lead.Id, Docebo_User__c: duRec.Id });
                                    console.log(back.success ? `   Linked Lead ${lead.Id} -> DU ${duRec.Id}` : `   Lead->DU back-ref failed: ${JSON.stringify(back.errors)}`);
                                } catch (be) {
                                    console.log(`   Lead back-reference error: ${be.message || be}`);
                                }
                            }
                        } catch (e) {
                            console.log(`   DU->Lead link error: ${e.message || e}`);
                        }

                } else {
                    console.log(`   🆕 User not found in Salesforce - creating Lead and Docebo_Users__c`);

                    // First ensure/create Docebo_Users__c
                    try {
                        const existingDU = await conn.sobject('Docebo_Users__c').findOne({ User_Unique_Id__c: processedData.User_Unique_Id__c });
                        if (existingDU) {
                            const duUpd = await conn.sobject('Docebo_Users__c').update({ ...processedData, Id: existingDU.Id });
                            console.log(duUpd.success ? `   ✅ Docebo_Users__c updated: ${existingDU.Id}` : `   ⚠️ Docebo_Users__c update failed: ${JSON.stringify(duUpd.errors)}`);
                        } else {
                            const duCreate = await conn.sobject('Docebo_Users__c').create(processedData);
                            console.log(duCreate.success ? `   ✅ Docebo_Users__c created: ${duCreate.id}` : `   ⚠️ Docebo_Users__c creation failed: ${JSON.stringify(duCreate.errors)}`);
                        }
                    } catch (e) {
                        console.log(`   ❌ Docebo_Users__c ensure error: ${e.message || e}`);
                    }

                    // Then attempt to create a new Lead (no Account creation)
                    try {
                        const leadCreate = await conn.sobject('Lead').create({
                            LastName: (processedData.Last_Name__c && processedData.Last_Name__c.trim() !== "") ? processedData.Last_Name__c : 'Unknown',
                            FirstName: processedData.First_Name__c || null,
                            Email: processedData.Email__c || null,
                            Company: processedData.Organization_Name__c || '-',
                            Title: processedData.Job_Title__c || '',
                            Website: processedData.Organization_URL__c || '',
                            Status: 'Open - Not Contacted',
                            Created_by_Docebo_API__c: true,
                            LeadSource: 'Docebo Platform'
                        });
                        if (leadCreate.success) {
                            console.log(`   ✅ Lead created: ${leadCreate.id}`);
                        } else {
                            console.log(`   ⚠️ Lead creation failed:`, leadCreate.errors);
                        }
                    } catch (e) {
                        console.log(`   ❌ Lead creation error (likely permission restriction): ${e.message || e}`);
                    }

                        // Link created records: Docebo_Users__c -> Lead and Lead -> DU
                        try {
                            const duRec = await conn.sobject('Docebo_Users__c').findOne({ User_Unique_Id__c: processedData.User_Unique_Id__c });
                            if (duRec && leadCreate && leadCreate.success) {
                                const link = await conn.sobject('Docebo_Users__c').update({ Id: duRec.Id, Lead__c: leadCreate.id, Email__c: email });
                                console.log(link.success ? `   Linked DU ${duRec.Id} -> Lead ${leadCreate.id}` : `   DU->Lead link failed: ${JSON.stringify(link.errors)}`);
                                try {
                                    const back = await conn.sobject('Lead').update({ Id: leadCreate.id, Docebo_User__c: duRec.Id });
                                    console.log(back.success ? `   Linked Lead ${leadCreate.id} -> DU ${duRec.Id}` : `   Lead->DU back-ref failed: ${JSON.stringify(back.errors)}`);
                                } catch (be) {
                                    console.log(`   Lead back-reference error: ${be.message || be}`);
                                }
                            }
                        } catch (e) {
                            console.log(`   Linking created DU/Lead error: ${e.message || e}`);
                        }

                }

                successCount++;

            } catch (error) {
                console.error(`   ❌ Error processing user ${user.user_id}:`, error.message);
                errorCount++;
            }
        }

        console.log('\n📊 TEST SUMMARY:');
        console.log(`✅ Successfully processed: ${successCount} users`);
        console.log(`❌ Errors: ${errorCount} users`);
        console.log(`📥 Total users tested: ${first5Users.length}`);

        if (successCount === first5Users.length) {
            console.log('\n🎉 ALL TESTS PASSED! The sync process should work correctly.');
        } else {
            console.log('\n⚠️ Some tests failed. Check the errors above.');
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Run the test
simple5UserTest().catch(console.error);
