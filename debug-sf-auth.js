#!/usr/bin/env node

/**
 * Debug Salesforce Authentication
 * 
 * This script helps diagnose authentication issues
 */

require('dotenv').config();
const axios = require('axios');

async function debugAuthentication() {
    console.log('🔍 DEBUGGING SALESFORCE AUTHENTICATION');
    console.log('=' .repeat(50));
    
    // Show configuration (sanitized)
    console.log('📋 Current Configuration:');
    console.log(`   Token URL: ${process.env.SF_TOKEN_URL}`);
    console.log(`   Instance: ${process.env.SF_API_INSTANCE}`);
    console.log(`   Username: ${process.env.SF_API_USER_NAME}`);
    console.log(`   Client ID: ${process.env.SF_API_CLIENT_ID ? process.env.SF_API_CLIENT_ID.substring(0, 15) + '...' : 'NOT SET'}`);
    console.log(`   Client Secret: ${process.env.SF_API_CLIENT_SECRET ? process.env.SF_API_CLIENT_SECRET.substring(0, 10) + '...' : 'NOT SET'}`);
    console.log(`   Password Length: ${process.env.SF_API_PASSWORD ? process.env.SF_API_PASSWORD.length : 0} characters`);
    console.log(`   Security Token: ${process.env.SF_API_ORG_PASS ? process.env.SF_API_ORG_PASS.substring(0, 5) + '...' : 'NOT SET'}`);
    console.log('');
    
    // Validate configuration
    console.log('🔧 Configuration Validation:');
    const issues = [];
    
    if (!process.env.SF_TOKEN_URL) issues.push('❌ SF_TOKEN_URL not set');
    if (!process.env.SF_API_INSTANCE) issues.push('❌ SF_API_INSTANCE not set');
    if (!process.env.SF_API_CLIENT_ID) issues.push('❌ SF_API_CLIENT_ID not set');
    if (!process.env.SF_API_CLIENT_SECRET) issues.push('❌ SF_API_CLIENT_SECRET not set');
    if (!process.env.SF_API_USER_NAME) issues.push('❌ SF_API_USER_NAME not set');
    if (!process.env.SF_API_PASSWORD) issues.push('❌ SF_API_PASSWORD not set');
    
    // Check password format
    if (process.env.SF_API_PASSWORD && process.env.SF_API_ORG_PASS) {
        const password = process.env.SF_API_PASSWORD;
        const token = process.env.SF_API_ORG_PASS;
        
        if (!password.endsWith(token)) {
            issues.push('⚠️ SF_API_PASSWORD should end with SF_API_ORG_PASS (password + token)');
        }
        
        if (token.length < 20) {
            issues.push('⚠️ Security token seems too short (should be ~25 characters)');
        }
    }
    
    if (issues.length === 0) {
        console.log('   ✅ All required fields are set');
    } else {
        issues.forEach(issue => console.log(`   ${issue}`));
    }
    console.log('');
    
    // Test authentication with detailed error info
    console.log('🔐 Testing Authentication...');
    
    try {
        const response = await axios.post(
            process.env.SF_TOKEN_URL,
            new URLSearchParams({
                grant_type: process.env.SF_API_GRANT_TYPE,
                client_id: process.env.SF_API_CLIENT_ID,
                client_secret: process.env.SF_API_CLIENT_SECRET,
                username: process.env.SF_API_USER_NAME,
                password: process.env.SF_API_PASSWORD
            }),
            {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                timeout: 30000
            }
        );
        
        console.log('✅ Authentication successful!');
        console.log(`   Access Token: ${response.data.access_token.substring(0, 20)}...`);
        console.log(`   Instance URL: ${response.data.instance_url}`);
        
    } catch (error) {
        console.log('❌ Authentication failed');
        
        if (error.response) {
            console.log(`   Status: ${error.response.status}`);
            console.log(`   Error: ${JSON.stringify(error.response.data, null, 2)}`);
            
            // Specific error guidance
            if (error.response.data.error === 'invalid_grant') {
                console.log('');
                console.log('🔧 INVALID_GRANT Troubleshooting:');
                console.log('   1. ❓ Is your username correct for PRODUCTION?');
                console.log('   2. ❓ Is your password correct?');
                console.log('   3. ❓ Is your security token current and correct?');
                console.log('   4. ❓ Is your user account active and not locked?');
                console.log('   5. ❓ Does your user have "API Enabled" permission?');
                console.log('   6. ❓ Are you connecting from a trusted IP address?');
                console.log('   7. ❓ Is your Connected App configured correctly?');
            }
            
        } else if (error.request) {
            console.log('   Network error - no response received');
            console.log(`   ${error.message}`);
        } else {
            console.log(`   Error: ${error.message}`);
        }
    }
    
    console.log('');
    console.log('💡 Next Steps:');
    console.log('   1. Verify your production username/password by logging into Salesforce web');
    console.log('   2. Reset your security token: Profile → Settings → Reset Security Token');
    console.log('   3. Check Network Access: Setup → Network Access');
    console.log('   4. Verify Connected App: Setup → App Manager → Your App');
    console.log('   5. Check user permissions: Setup → Users → Your User → API Enabled');
}

debugAuthentication().catch(console.error);
