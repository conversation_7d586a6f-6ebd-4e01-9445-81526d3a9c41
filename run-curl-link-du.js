#!/usr/bin/env node

require('dotenv').config();
const { execFile } = require('child_process');
const getSFToken = require('./platform/salesforce/common/getAccessToken');

function runCurl(args) {
  return new Promise((resolve) => {
    const child = execFile('curl.exe', args, { windowsHide: true }, (error, stdout, stderr) => {
      resolve({ error, stdout, stderr });
    });
  });
}

async function main() {
  const duId = process.argv[2] || 'a2EPo00000WRNtfMAH';
  const leadId = process.argv[3] || '00QPo00000nrCYOMA2';
  const email = process.argv[4] || '<EMAIL>';

  console.log('🔐 Getting Salesforce token via client_credentials...');
  const tokenRes = await getSFToken();
  if (tokenRes.status !== 200) {
    console.error('❌ Failed to get token:', tokenRes.message || tokenRes);
    process.exit(1);
  }
  const { accessToken, instanceUrl } = tokenRes.data;
  console.log('✅ Token OK. Instance:', instanceUrl);

  const url = `${instanceUrl}/services/data/v60.0/sobjects/Docebo_Users__c/${duId}`;
  const body = JSON.stringify({ Lead__c: leadId, Email__c: email });

  const curlArgs = [
    '--silent', '--show-error', '--write-out', '\nhttp:%{http_code}',
    '--request', 'PATCH',
    url,
    '--header', `Authorization: Bearer ${accessToken}`,
    '--header', 'Content-Type: application/json',
    '--data', body
  ];

  console.log('\n🧪 Running curl.exe with args:', JSON.stringify(curlArgs));
  const { error, stdout, stderr } = await runCurl(curlArgs);
  if (stderr) console.error('\nSTDERR:', stderr);
  if (error) {
    console.error('\n❌ curl.exe failed:', error.message);
    process.exit(1);
  }
  console.log('\nSTDOUT:', stdout);

  // Interpret result code
  const httpLine = stdout.split('\n').find(l => l.startsWith('http:')) || '';
  const code = httpLine.replace('http:', '').trim();
  console.log('HTTP code:', code || '(unknown)');

  if (code !== '204' && code !== '200') {
    console.error('❗ Unexpected HTTP code from PATCH');
    process.exit(1);
  }

  console.log('✅ cURL PATCH executed successfully.');
}

main().catch(err => {
  console.error('💥 Unexpected error:', err.message);
  process.exit(1);
});

