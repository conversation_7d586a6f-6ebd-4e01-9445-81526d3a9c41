#!/usr/bin/env node

require('dotenv').config();
const getSFToken = require('./platform/salesforce/common/getAccessToken');
const getConnection = require('./platform/salesforce/common/getConnection');

function mask(str, start = 6, end = 4) {
  if (!str || typeof str !== 'string') return String(str);
  if (str.length <= start + end) return '*'.repeat(str.length);
  return `${str.slice(0, start)}...${str.slice(-end)}`;
}

async function main() {
  console.log('🔎 Checking Salesforce OAuth configuration...');
  const env = process.env;
  console.log('• SF_TOKEN_URL:', env.SF_TOKEN_URL || '(missing)');
  console.log('• SF_API_GRANT_TYPE:', env.SF_API_GRANT_TYPE || '(missing)');
  console.log('• SF_API_CLIENT_ID:', env.SF_API_CLIENT_ID ? mask(env.SF_API_CLIENT_ID) : '(missing)');
  console.log('• SF_API_CLIENT_SECRET:', env.SF_API_CLIENT_SECRET ? mask(env.SF_API_CLIENT_SECRET) : '(missing)');
  if (env.SF_API_GRANT_TYPE === 'password') {
    console.log('• SF_API_USER_NAME:', env.SF_API_USER_NAME || '(missing)');
    console.log('• SF_API_PASSWORD:', env.SF_API_PASSWORD ? `${env.SF_API_PASSWORD.length} chars` : '(missing)');
  }

  console.log('\n🔐 Attempting to fetch Salesforce access token...');
  const tokenRes = await getSFToken();
  if (tokenRes.status !== 200) {
    console.error('❌ Token fetch failed');
    console.error('   Status:', tokenRes.status);
    console.error('   Message:', tokenRes.message || tokenRes);
    process.exit(1);
  }
  console.log('✅ Token acquired. Instance URL:', tokenRes.data.instanceUrl);
  console.log('   Access token length:', tokenRes.data.accessToken.length);

  console.log('\n🌐 Testing JSforce connection with a simple query...');
  const conn = await getConnection();
  if (!conn || !conn.accessToken) {
    console.error('❌ Invalid Salesforce connection object from getConnection');
    console.error('   Details:', conn);
    process.exit(1);
  }

  try {
    const res = await conn.query('SELECT Id FROM Lead LIMIT 1');
    console.log('✅ SOQL query succeeded. Records:', res.totalSize);
    if (res.records && res.records[0]) console.log('   Sample Lead Id:', res.records[0].Id);
  } catch (e) {
    console.error('⚠️ Query failed, but auth may still be valid. Error:');
    console.error('  ', e && (e.message || e.errorCode || e.toString()));
    process.exit(1);
  }
}

main().catch(err => {
  console.error('💥 Unexpected error in SF auth check:', err.message);
  process.exit(1);
});

